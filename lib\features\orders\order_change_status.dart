import 'package:flutter/material.dart';

class OrdersScreenWithBottomSheet extends StatelessWidget {
  final List<Map<String, dynamic>> orders = [
    {'id': '001', 'date': '2025-01-14', 'status': 'Pending', 'total': 120.5},
    {'id': '002', 'date': '2025-01-13', 'status': 'Completed', 'total': 250.0},
    {'id': '003', 'date': '2025-01-12', 'status': 'Cancelled', 'total': 75.0},
  ];

   OrdersScreenWithBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Orders'),
        backgroundColor: Colors.indigo,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(8.0),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return Card(
            elevation: 5,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: ListTile(
              title: Text(
                'Order #${order['id']}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Date: ${order['date']}'),
                  Text('Total: \$${order['total']}'),
                ],
              ),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getStatusColor(order['status']),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  order['status'],
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
              onTap: () => _showChangeStatusSheet(context, order),
            ),
          );
        },
      ),
    );
  }

  void _showChangeStatusSheet(BuildContext context, Map<String, dynamic> order) {
    final statuses = ['Pending', 'Completed', 'Cancelled'];

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Change Order Status',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ...statuses.map((status) {
                return ListTile(
                  leading: Icon(
                    order['status'] == status ? Icons.check_circle : Icons.circle_outlined,
                    color: order['status'] == status ? Colors.green : Colors.grey,
                  ),
                  title: Text(
                    status,
                    style: TextStyle(
                      fontSize: 18,
                      color: order['status'] == status ? Colors.green : Colors.black,
                    ),
                  ),
                  onTap: () {
                    // تحديث حالة الطلب
                    Navigator.pop(context); // إغلاق الـ Bottom Sheet
                    order['status'] = status; // تحديث الحالة (للتجربة فقط)
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Order status changed to $status')),
                    );
                  },
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Pending':
        return Colors.orange;
      case 'Completed':
        return Colors.green;
      case 'Cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
