import 'dart:async';

import 'package:flutter_temp/features/general/presentation/controllers/general_controller.dart';
import 'package:get/get.dart';

import '../../../../core/services/app_lifecycle_service.dart';
import '../../../../core/services/location_service.dart';
import '../../data/home_provider.dart';
import '../../data/models/customer_visits.dart';
// import '../../data/model/home/<USER>';

class HomeController extends GetxController {
  final HomeProvider homeProvider;
  final locationService = LocationService();
  late final AppLifecycleService _lifecycleService;

  HomeController({required this.homeProvider});
  int currentSlider = 0;
  DistributorVisits? distributorVisits;

  @override
  void onInit() {
    super.onInit();
    _lifecycleService = Get.find<AppLifecycleService>();

    // Check tracking status from lifecycle service
    _lifecycleService.isTrackingEnabled().then((v) {
      isChecked = v;
      update();
    });
    update();
  }

  var selectedCat = "red";

  var isChecked = true;

  var loadingDistributorVisits = false.obs;
  Future<void> loadDistributorVisits() async {
    loadingDistributorVisits.value = true;
    updateIFNeed();

    print("home Started");

    await homeProvider.get_distributor_visits().then((result) {
      print("distributorVisits Loaded ");
      distributorVisits = result.body!;
      updateIFNeed();
      refresh();
    }, onError: (err, stackTrace) {
      print("home Started $stackTrace");

      print(err);
    });
    loadingDistributorVisits.value = false;
  }

  void toggleTracking(bool value) async {
    isChecked = value;
    if (isChecked) {
      await _lifecycleService.startTracking();
    } else {
      await _lifecycleService.stopTracking();
    }
    update();
  }
}
