name: Build Flutter APK and Upload to Release

on:
 push:
    branches:
      - main
      # - test 
  # push:
  #   tags:
  #     - 'v*.*.*' # Trigger on tags that follow semantic versioning

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    # Checkout the repository
    - name: Checkout code
      uses: actions/checkout@v3

    # Set up Flutter environment
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.5' #3.19.0

    # Install dependencies
    - name: Install dependencies
      run: flutter pub get

    # Build APK
    - name: Build APK
      run: flutter build apk --release

    # Archive the APK
    - name: Archive APK
      uses: actions/upload-artifact@v4
      with:
        name: app-release
        path: build/app/outputs/flutter-apk/app-release.apk

  release:
    needs: build
    runs-on: ubuntu-latest
    steps:
    # Checkout code
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Fetch Commit Messages
      id: commits
      run: |
        COMMITS=$(git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 2>/dev/null || echo "")..HEAD)
        echo "commits=$COMMITS" >> $GITHUB_ENV

    # Download the APK from the build job
    - name: Download APK artifact
      uses: actions/download-artifact@v4
      with:
        name: app-release
        path: build/

    # Upload APK to release page
    - name: Upload APK to GitHub Release
      uses: ncipollo/release-action@v1
      with:
        artifacts: build/app-release.apk
        token: ${{ secrets.GITHUB_TOKEN }}
        tag: "v${{ github.run_number }}" 
        name: "Release ${{ github.run_number }}"
        # tag: ${{ github.ref_name }}
        # name: "Release ${{ github.ref_name }}"
        body: |
          ## Release Notes
          - APK built successfully for release.
          - Branch: `${{ github.ref_name }}`.
          - Recent commits:
            ${{ env.commits }}
#        body: |
#          This release includes the following:
#          - APK built for release.
