import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constants.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../home/<USER>/models/customer_visits.dart';
import 'controllers/order_details_controller.dart';

class OrderDetailsScreen extends StatelessWidget {
  final CustomerVisits visit;

  OrderDetailsScreen({super.key, required this.visit}) {
    // Initialize controller
    Get.put(OrderDetailsController(initialVisit: visit));
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderDetailsController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Visit Details'),
        backgroundColor: Constants.primaryColor,
      ),
      body: Obx(() => Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Visit #${visit.name ?? "N/A"}',
                      style: const TextStyle(
                          fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    Text('Customer: ${visit.customer ?? "N/A"}'),
                    Text('Date: ${visit.visitDate ?? "N/A"}'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('Status: ', style: TextStyle(fontSize: 16)),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color:
                                controller.getStatusColor(visit.status ?? ""),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            visit.status ?? "Unknown",
                            style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    if (visit.coordinates != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Location:',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'Lat: ${visit.coordinates?.lat}, Lon: ${visit.coordinates?.lon}',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.map,
                                    color: Constants.primaryColor),
                                onPressed: () {
                                  _openMap(visit.coordinates?.lat,
                                      visit.coordinates?.lon);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                  ],
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Card(
                    elevation: 3,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Financial Information',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 16),
                          _buildFinancialInfoRow(
                              'Outstanding', visit.outstanding),
                          _buildFinancialInfoRow(
                              'Credit Limit', visit.creditLimit),
                          _buildFinancialInfoRow('Balance', visit.balance),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: controller.isLoading.value
                          ? null
                          : () => _showChangeStatusSheet(context, visit),
                      icon: controller.isLoading.value
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                  strokeWidth: 2, color: Colors.white))
                          : const Icon(Icons.edit),
                      label: Text(controller.isLoading.value
                          ? 'Updating...'
                          : 'Change Status'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Constants.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () {
                        Get.back();
                      },
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Back'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          )),
    );
  }

  Widget _buildFinancialInfoRow(String label, double? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: const TextStyle(fontSize: 16),
          ),
          Text(
            value != null ? '\$${value.toStringAsFixed(2)}' : 'N/A',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: value != null && value > 0 ? Colors.green : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  void _openMap(double? lat, double? lon) {
    if (lat == null || lon == null) return;

    final url = 'https://www.google.com/maps/search/?api=1&query=$lat,$lon';
    launchUrl(Uri.parse(url));
  }

  // Status color method moved to controller
}

void _showChangeStatusSheet(BuildContext context, CustomerVisits visit) {
  // Define available statuses - these must match exactly what the API expects
  final statuses = ['Open', 'Start', 'Closed'];
  final controller = Get.find<OrderDetailsController>();

  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (BuildContext context) {
      return Obx(() {
        return Container(
          padding: const EdgeInsets.all(24.0),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Center(
                child: Text(
                  'Change Visit Status',
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 24),
              if (controller.isLoading.value)
                const Center(
                  child: Column(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Updating status...',
                          style: TextStyle(fontSize: 16)),
                    ],
                  ),
                )
              else
                ...statuses.map((status) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                      color: visit.status == status
                          ? controller.getStatusColor(status).withOpacity(0.1)
                          : Colors.white,
                    ),
                    child: RadioListTile<String>(
                      title: Text(
                        status,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: visit.status == status
                              ? FontWeight.bold
                              : FontWeight.normal,
                          color: visit.status == status
                              ? controller.getStatusColor(status)
                              : Colors.black,
                        ),
                      ),
                      value: status,
                      groupValue: visit.status,
                      activeColor: controller.getStatusColor(status),
                      onChanged: (value) async {
                        if (value == null) {
                          Navigator.pop(context);
                          return;
                        }

                        // Update status using controller
                        final success =
                            await controller.updateVisitStatus(value);

                        if (success) {
                          Navigator.pop(context);
                        }
                      },
                    ),
                  );
                }),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: controller.isLoading.value
                          ? null
                          : () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      child: const Text('Cancel',
                          style: TextStyle(fontSize: 16, color: Colors.white)),
                    ),
                  ),
                  // if (false) // Set to true for debugging
                ],
              ),
            ],
          ),
        );
      });
    },
  );
}
