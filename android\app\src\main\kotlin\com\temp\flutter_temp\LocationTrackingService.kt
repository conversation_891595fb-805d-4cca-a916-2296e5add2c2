package com.temp.flutter_temp

import android.Manifest
import android.app.*
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import io.flutter.plugin.common.MethodChannel

class LocationTrackingService : Service(), LocationListener {

    companion object {
        private const val TAG = "LocationTrackingService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "location_tracking_channel"
        private const val CHANNEL_NAME = "Location Tracking"
        private const val ACTION_STOP_TRACKING = "STOP_TRACKING"

        // Location update parameters
        private const val MIN_TIME_BETWEEN_UPDATES = 30000L // 30 seconds
        private const val MIN_DISTANCE_CHANGE_FOR_UPDATES = 2f // 2 meters

        var isServiceRunning = false
            private set
    }
    
    private val binder = LocationServiceBinder()
    private lateinit var locationManager: LocationManager
    private lateinit var notificationManager: NotificationManager
    private var wakeLock: PowerManager.WakeLock? = null
    private var startTime: Long = 0
    private var locationCount = 0
    private var lastLocationTime: Long = 0
    private val notificationUpdateHandler = Handler(Looper.getMainLooper())
    private var notificationUpdateRunnable: Runnable? = null
    private val syncHandler = Handler(Looper.getMainLooper())
    private var syncRunnable: Runnable? = null

    // Method channel for communication with Flutter
    var methodChannel: MethodChannel? = null
    
    inner class LocationServiceBinder : Binder() {
        fun getService(): LocationTrackingService = this@LocationTrackingService
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")
        
        locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        createNotificationChannel()
        acquireWakeLock()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Service started with action: ${intent?.action}")

        when (intent?.action) {
            "START_LOCATION_TRACKING" -> {
                startTime = System.currentTimeMillis()
                startLocationTracking()
            }
            "STOP_LOCATION_TRACKING" -> stopLocationTracking()
            ACTION_STOP_TRACKING -> {
                Log.d(TAG, "Stop tracking requested from notification")
                // Update shared preferences to prevent restart
                val prefs = getSharedPreferences("location_tracking_prefs", Context.MODE_PRIVATE)
                prefs.edit().putBoolean("location_tracking_enabled", false).apply()

                // Notify Flutter about the change
                methodChannel?.invokeMethod("onTrackingStatusChanged", mapOf(
                    "isTracking" to false
                ))

                // Stop tracking from notification action
                stopLocationTracking()
            }
            else -> {
                startTime = System.currentTimeMillis()
                startLocationTracking()
            }
        }

        // Return START_STICKY to restart service if killed by system
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Service destroyed")
        stopLocationTracking()
        releaseWakeLock()
        isServiceRunning = false
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Used for tracking location in the background"
                setShowBadge(false)
                // Make notification non-dismissible and silent
                setSound(null, null)
                enableVibration(false)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            }

            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Create stop action
        val stopIntent = Intent(this, LocationTrackingService::class.java).apply {
            action = ACTION_STOP_TRACKING
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 1, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Calculate elapsed time
        val elapsedTime = if (startTime > 0) {
            val elapsed = (System.currentTimeMillis() - startTime) / 1000
            val hours = elapsed / 3600
            val minutes = (elapsed % 3600) / 60
            val seconds = elapsed % 60
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            "00:00:00"
        }

        // Format last location time
        val lastLocationText = if (lastLocationTime > 0) {
            val timeSince = (System.currentTimeMillis() - lastLocationTime) / 1000
            "آخر موقع: منذ ${timeSince}ث"
        } else {
            "في انتظار الموقع..."
        }

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("🔴 تتبع الموقع نشط")
            .setContentText("المدة: $elapsedTime • المواقع: $locationCount")
            .setSubText(lastLocationText)
            .setSmallIcon(android.R.drawable.ic_menu_mylocation)
            .setContentIntent(pendingIntent)
            .setOngoing(true) // Makes notification non-dismissible
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setAutoCancel(false)
            .setShowWhen(true)
            .setWhen(startTime)
            .setUsesChronometer(true)
            .setChronometerCountDown(false)
            .addAction(
                android.R.drawable.ic_menu_close_clear_cancel,
                "إيقاف التتبع",
                stopPendingIntent
            )
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("تتبع الموقع نشط\nالمدة: $elapsedTime\nعدد المواقع: $locationCount\n$lastLocationText"))
            .build()
    }
    
    private fun acquireWakeLock() {
        try {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "$TAG::WakeLock"
            )
            wakeLock?.acquire(10*60*1000L /*10 minutes*/)
            Log.d(TAG, "Wake lock acquired")
        } catch (e: Exception) {
            Log.e(TAG, "Error acquiring wake lock", e)
        }
    }
    
    private fun releaseWakeLock() {
        try {
            wakeLock?.let {
                if (it.isHeld) {
                    it.release()
                    Log.d(TAG, "Wake lock released")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing wake lock", e)
        }
    }
    
    private fun startLocationTracking() {
        if (!hasLocationPermissions()) {
            Log.e(TAG, "Location permissions not granted")
            return
        }
        
        try {
            // Start foreground service with persistent notification
            startForeground(NOTIFICATION_ID, createNotification())
            isServiceRunning = true
            
            // Request location updates from both GPS and Network providers
            if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    MIN_TIME_BETWEEN_UPDATES,
                    MIN_DISTANCE_CHANGE_FOR_UPDATES,
                    this
                )
                Log.d(TAG, "GPS location updates requested")
            }
            
            if (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                locationManager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    MIN_TIME_BETWEEN_UPDATES,
                    MIN_DISTANCE_CHANGE_FOR_UPDATES,
                    this
                )
                Log.d(TAG, "Network location updates requested")
            }
            
            Log.d(TAG, "Location tracking started")

            // Start periodic notification updates
            startNotificationUpdates()

            // Start periodic sync (every 5 minutes)
            startPeriodicSync()

        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception when requesting location updates", e)
        } catch (e: Exception) {
            Log.e(TAG, "Error starting location tracking", e)
        }
    }
    
    private fun stopLocationTracking() {
        try {
            Log.d(TAG, "Stopping location tracking...")

            // Stop location updates
            locationManager.removeUpdates(this)

            // Stop notification updates
            stopNotificationUpdates()

            // Stop periodic sync
            stopPeriodicSync()

            // Stop foreground service and remove notification
            stopForeground(true)

            // Update service state
            isServiceRunning = false

            // Reset counters
            locationCount = 0
            startTime = 0
            lastLocationTime = 0

            // Stop the service completely
            stopSelf()

            Log.d(TAG, "Location tracking stopped successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping location tracking", e)
        }
    }
    
    private fun hasLocationPermissions(): Boolean {
        return ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED &&
        ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    // LocationListener implementation
    override fun onLocationChanged(location: Location) {
        Log.d(TAG, "Location changed: ${location.latitude}, ${location.longitude}")

        // Update counters
        locationCount++
        lastLocationTime = System.currentTimeMillis()

        // Update notification with new information
        val notification = createNotification()
        notificationManager.notify(NOTIFICATION_ID, notification)

        // Send location to Flutter
        methodChannel?.invokeMethod("onLocationUpdate", mapOf(
            "latitude" to location.latitude,
            "longitude" to location.longitude,
            "timestamp" to System.currentTimeMillis(),
            "accuracy" to location.accuracy,
            "provider" to location.provider
        ))
    }
    
    override fun onProviderEnabled(provider: String) {
        Log.d(TAG, "Provider enabled: $provider")
    }
    
    override fun onProviderDisabled(provider: String) {
        Log.d(TAG, "Provider disabled: $provider")
    }
    
    @Deprecated("Deprecated in API level 29")
    override fun onStatusChanged(provider: String?, status: Int, extras: android.os.Bundle?) {
        Log.d(TAG, "Provider status changed: $provider, status: $status")
    }

    private fun startNotificationUpdates() {
        notificationUpdateRunnable = object : Runnable {
            override fun run() {
                try {
                    val notification = createNotification()
                    notificationManager.notify(NOTIFICATION_ID, notification)
                    notificationUpdateHandler.postDelayed(this, 1000) // Update every second
                } catch (e: Exception) {
                    Log.e(TAG, "Error updating notification", e)
                }
            }
        }
        notificationUpdateHandler.post(notificationUpdateRunnable!!)
    }

    private fun stopNotificationUpdates() {
        notificationUpdateRunnable?.let {
            notificationUpdateHandler.removeCallbacks(it)
        }
        notificationUpdateRunnable = null
    }

    private fun startPeriodicSync() {
        syncRunnable = object : Runnable {
            override fun run() {
                try {
                    // Request sync from Flutter
                    methodChannel?.invokeMethod("requestSync", null)
                    // Schedule next sync in 5 minutes
                    syncHandler.postDelayed(this, 5 * 60 * 1000L)
                } catch (e: Exception) {
                    Log.e(TAG, "Error requesting sync", e)
                }
            }
        }
        // Start first sync after 1 minute
        syncHandler.postDelayed(syncRunnable!!, 60 * 1000L)
    }

    private fun stopPeriodicSync() {
        syncRunnable?.let {
            syncHandler.removeCallbacks(it)
        }
        syncRunnable = null
    }
}
