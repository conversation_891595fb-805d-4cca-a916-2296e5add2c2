import 'package:get/get.dart';

import '../../../core/api_provider.dart';
import '../../../features/orders/data/models/DistributorStore.dart';

class StoreProvider extends ApiProvider {
  Future<DistributorStore> distributor_store({
    int limit_page_length = 400,
    int page = 1,
  }) =>
      sendPostDio<DistributorStore>(
          'rukn_distributor.api.distributor_store',
          {
            "limit_page_length": limit_page_length,
            "page": page,
          },
          contentType: "'application/json",
          enableContentType: true,
          decoder: (obj) => DistributorStore.fromJson(obj));
}
