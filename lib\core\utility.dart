import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
enum TypeSocial {
 facebook,instagram
}
class Utility {
  static Future<void> appLaunchUrl(String url) async {
    final Uri url0 = Uri.parse(url);

    if (!await launchUrl(url0)) {
      throw Exception('Could not launch $url0');
    }
  }

  static String? validatorGeneral(value) {
    if (value!.isEmpty) {
      return "filedNullError".tr;
    }
    /*else if (!GetUtils.isEmail(value)) {
          return kInvalidEmailError.tr;
        }*/
    return null;
  }

static String? validatorPassword(value) {
    if (value!.isEmpty) {
      return "filedNullError".tr;
    }
    else if (value.length < 9) {
          return "password at least 9 characters".tr;
        }
    return null;
  }
  static String? validatorEmail(value) {
    if (value!.isEmpty) {
      return "filedNullError".tr;
    } else if (!GetUtils.isEmail(value)) {
      return "invalidEmailError".tr;
    }
    return null;
  }

}

