class DistributorStore {
  Message? message;

  DistributorStore({this.message});

  DistributorStore.fromJson(Map<String, dynamic> json) {
    message =
        json['message'] != null ? new Message.fromJson(json['message']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.message != null) {
      data['message'] = this.message!.toJson();
    }
    return data;
  }
}

class Message {
  Status? status;
  Data? data;
  int? page;
  int? pages;

  Message({this.status, this.data, this.page, this.pages});

  Message.fromJson(Map<String, dynamic> json) {
    status =
        json['status'] != null ? new Status.fromJson(json['status']) : null;
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    page = json['page'];
    pages = json['pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.status != null) {
      data['status'] = this.status!.toJson();
    }
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['page'] = this.page;
    data['pages'] = this.pages;
    return data;
  }
}

class Status {
  String? message;
  bool? success;
  int? code;

  Status({this.message, this.success, this.code});

  Status.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    success = json['success'];
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['success'] = this.success;
    data['code'] = this.code;
    return data;
  }
}

class Data {
  List<ItemsStock>? itemsStock;

  Data({this.itemsStock});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['items_stock'] != null) {
      itemsStock = <ItemsStock>[];
      json['items_stock'].forEach((v) {
        itemsStock!.add(ItemsStock.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.itemsStock != null) {
      data['items_stock'] = this.itemsStock!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ItemsStock {
  String? itemName;
  String? itemCode;
  String? itemGroup;
  String? itemImage;
  String? defaultUom;
  double? qtyAfterTransaction;
  double? priceListRate;
  var selectedQuantity = 0;
  ItemsStock(
      {this.itemName,
      this.itemCode,
      this.itemGroup,
      this.itemImage,
      this.defaultUom,
      this.qtyAfterTransaction,
      this.priceListRate});

  ItemsStock.fromJson(Map<String, dynamic> json) {
    itemName = json['item_name'];
    itemCode = json['item_code'];
    itemGroup = json['item_group'];
    itemImage = json['item_image'];
    defaultUom = json['default_uom'];
    qtyAfterTransaction =
        double.tryParse("${json['qty_after_transaction']}") ?? 0;
    priceListRate = double.tryParse("${json['price_list_rate']}") ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['item_name'] = this.itemName;
    data['item_code'] = this.itemCode;
    data['item_group'] = this.itemGroup;
    data['item_image'] = this.itemImage;
    data['default_uom'] = this.defaultUom;
    data['qty_after_transaction'] = this.qtyAfterTransaction;
    data['price_list_rate'] = this.priceListRate;
    return data;
  }
}
