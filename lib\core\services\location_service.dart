// lib/core/services/location_service.dart
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../features/general/presentation/controllers/general_controller.dart';
import '../constant.dart';

import 'location_database.dart';
import 'native_location_service.dart';

class LocationService {
  final _dio = Dio(); // Initialize Dio instance as a class field

  // Track if we're currently tracking a customer visit
  String? _activeCustomerVisitId;
  String? _activeCustomerName;

  /// Request location permissions and return whether they were granted
  Future<bool> requestLocationPermission() async {
    // Check and request location permissions
    if (Platform.isAndroid) {
      // For Android, we need to request both permissions
      final status = await Permission.locationWhenInUse.request();

      // If the basic permission is granted, request background permission
      if (status.isGranted) {
        final backgroundStatus = await Permission.locationAlways.request();
        return backgroundStatus.isGranted;
      }
      return false;
    } else if (Platform.isIOS) {
      // For iOS, request location always permission
      final status = await Permission.locationAlways.request();
      return status.isGranted;
    }

    return false;
  }

  /// Check if location permissions are granted
  Future<bool> checkLocationPermission() async {
    if (Platform.isAndroid) {
      final foregroundStatus = await Permission.locationWhenInUse.status;
      final backgroundStatus = await Permission.locationAlways.status;
      return foregroundStatus.isGranted && backgroundStatus.isGranted;
    } else if (Platform.isIOS) {
      final status = await Permission.locationAlways.status;
      return status.isGranted;
    }
    return false;
  }

  Future<void> startTracking() async {
    // Check if permissions are granted
    bool hasPermission = await checkLocationPermission();

    if (!hasPermission) {
      // Request permissions if not granted
      hasPermission = await requestLocationPermission();

      if (!hasPermission) {
        // Show a dialog if permissions are denied
        Get.dialog(
          AlertDialog(
            title: const Text('Location Permission Required'),
            content: const Text(
                'This app needs location permissions to track your position. Please grant the permissions in the app settings.'),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  openAppSettings();
                },
                child: const Text('Open Settings'),
              ),
            ],
          ),
        );
        return;
      }
    }

    // Start native location tracking
    final success = await NativeLocationService.instance.startLocationTracking();
    if (success) {
      print('Started native location tracking');
    } else {
      print('Failed to start native location tracking');
    }
  }

  Future<void> stopTracking() async {

    final success = await NativeLocationService.instance.stopLocationTracking();
    if (success) {
      print('Stopped native location tracking');
    } else {
      print('Failed to stop native location tracking');
    }

    await syncLocationsToApi();
  }

  Future<void> syncLocationsToApi() async {
    try {
      print('🔄 Starting location sync...');

      // Get all locations from database
      final locations = await LocationDatabase.instance.getAllLocations();
      print('📍 Found ${locations.length} locations to sync');

      if (locations.isEmpty) {
        print('✅ No locations to sync');
        return;
      }

      // Transform locations to required format
      final coordinates = locations
          .map((loc) => {
                "lat": loc['latitude'],
                "lon": loc['longitude'],
                "timestamp": loc['timestamp'],
              })
          .toList();

      final data = {"group": "group", "coordinates": coordinates};

      // Get token
      final token = Get.find<GeneralController>().token();
      print('🔑 Token available: ${token != null}');

      if (token == null) {
        print('❌ No authentication token available');
        return;
      }

      print('🌐 Sending ${coordinates.length} coordinates to API...');
      print('📡 API URL: ${baseApiUrl}rukn_distributor.api.distributor_tracking');

      final response = await _dio.post(
        '${baseApiUrl}rukn_distributor.api.distributor_tracking',
        options: Options(
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json'
          },
        ),
        data: data,
      );

      print('📨 API Response: ${response.statusCode}');
      print('📄 Response data: ${response.data}');

      if (response.statusCode == 200) {
        // Clear local database after successful sync
        await LocationDatabase.instance.deleteAllLocations();
        print('✅ Locations synced successfully and cleared from local DB');
      } else {
        print('❌ Failed to sync locations: ${response.statusMessage}');
      }
    } catch (e) {
      print('❌ Error syncing locations: $e');
      if (e is DioException) {
        print('🌐 Dio Error Details:');
        print('   Status Code: ${e.response?.statusCode}');
        print('   Response Data: ${e.response?.data}');
        print('   Error Message: ${e.message}');
      }
    }
  }

  // This method is used by the background callback
  Future<void> saveLocationData({
    required double latitude,
    required double longitude,
    required String timestamp,
  }) async {
    // Check if there's an active customer visit
    final activeVisit =
        await LocationDatabase.instance.getActiveCustomerVisit();

    final locationData = {
      'latitude': latitude,
      'longitude': longitude,
      'timestamp': timestamp,
      'customer_visit': activeVisit != null ? activeVisit['visit_id'] : null,
    };
    await LocationDatabase.instance.insert(locationData);
  }

  Future<bool> isTrackingEnabled() async {
    return await NativeLocationService.instance.isLocationTrackingEnabled();
  }

  // Start tracking a specific customer visit
  Future<bool> startCustomerVisitTracking(
      String customerName, String visitId) async {
    try {
      // Check if permissions are granted
      bool hasPermission = await checkLocationPermission();

      if (!hasPermission) {
        // Request permissions if not granted
        hasPermission = await requestLocationPermission();

        if (!hasPermission) {
          return false;
        }
      }

      // Store the active customer visit info
      _activeCustomerVisitId = visitId;
      _activeCustomerName = customerName;

      // Start customer visit tracking using native service
      await NativeLocationService.instance.startCustomerVisitTracking(visitId, customerName);

      return true;
    } catch (e) {
      print('Error starting customer visit tracking: $e');
      return false;
    }
  }

  // End tracking for the current customer visit
  Future<bool> endCustomerVisitTracking() async {
    try {
      if (_activeCustomerVisitId == null) {
        // No active customer visit to end
        return false;
      }

      // End customer visit tracking using native service
      await NativeLocationService.instance.stopCustomerVisitTracking();

      // Sync the customer visit data to the API
      await _syncCustomerVisitToApi(_activeCustomerVisitId!);
      await syncLocationsToApi();

      // Clear the active customer visit
      _activeCustomerVisitId = null;
      _activeCustomerName = null;

      return true;
    } catch (e) {
      print('Error ending customer visit tracking: $e');
      return false;
    }
  }

  // Check if there's an active customer visit
  Future<Map<String, dynamic>?> getActiveCustomerVisit() async {
    return await LocationDatabase.instance.getActiveCustomerVisit();
  }

  // Sync customer visit data to the API
  Future<void> _syncCustomerVisitToApi(String visitId) async {
    try {
      // Get all locations for this customer visit
      final locations =
          await LocationDatabase.instance.getCustomerVisitLocations(visitId);

      if (locations.isEmpty) return;

      // Get the visit details
      final db = await LocationDatabase.instance.database;
      final visits = await db.query('customer_visits',
          where: 'visit_id = ?', whereArgs: [visitId], limit: 1);

      if (visits.isEmpty) return;

      final visit = visits.first;

      // Transform locations to required format
      final coordinates = locations
          .map((loc) => {
                "lat": loc['latitude'],
                "lon": loc['longitude'],
                "timestamp": loc['timestamp'],
              })
          .toList();

      // Create data payload with customer_visit field
      final data = {"customer_visit": visitId, "coordinates": coordinates};

      // Send to API
      final response = await _dio.post(
        '${baseApiUrl}rukn_distributor.api.distributor_tracking',
        options: Options(
          headers: {
            'Authorization': (Get.find<GeneralController>().token())!,
            'Content-Type': 'application/json'
          },
        ),
        data: data,
      );

      if (response.statusCode == 200) {
        print('Customer visit locations synced successfully');
      } else {
        print(
            'Failed to sync customer visit locations: ${response.statusMessage}');
      }
    } catch (e) {
      print('Error syncing customer visit locations: $e');
    }
  }
}
