import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constant.dart';
import 'package:flutter_temp/core/services/location_service.dart';
import 'package:flutter_temp/features/orders/data/models/shopping_cart.dart';
import 'package:get/get.dart';

import '../../home/<USER>/models/customer_visits.dart';
import '../../home/<USER>/controllers/home_controller.dart';
import '../data/models/DistributorStore.dart';
import '../data/order_provider.dart';

// import '../../data/model/home/<USER>';

class OrderController extends GetxController {
  final OrderProvider orderProvider;
  OrderController({required this.orderProvider});

  final TextEditingController paymentTextController = TextEditingController();
  final TextEditingController refoudTextController = TextEditingController();

  final LocationService locationService = LocationService();

  // Track customer visit tracking state
  RxBool isCustomerVisitTracking = false.obs;

  CustomerVisits? selectedOrder;
  DistributorStore? distributorStore;

  @override
  void onInit() {
    super.onInit();
    _checkActiveVisit();
  }

  // Check if there's an active visit when the controller initializes
  Future<void> _checkActiveVisit() async {
    final activeVisit = await locationService.getActiveCustomerVisit();
    if (activeVisit != null && selectedOrder != null) {
      // If the active visit matches the current customer, update the tracking state
      if (activeVisit['visit_id'] == selectedOrder!.name) {
        isCustomerVisitTracking.value = true;
      }
    }
  }

  // Start tracking for the current customer visit
  Future<void> startCustomerVisitTracking() async {
    if (selectedOrder == null ||
        selectedOrder!.name == null ||
        selectedOrder!.customer == null) {
      showError("Customer information is missing");
      return;
    }

    // Check if general tracking is enabled in HomeController
    final homeController = Get.find<HomeController>();
    final wasGeneralTrackingEnabled = homeController.isChecked;

    // Start customer visit tracking
    final success = await locationService.startCustomerVisitTracking(
        selectedOrder!.customer!, selectedOrder!.name!);

    if (success) {
      isCustomerVisitTracking.value = true;

      // If general tracking was disabled, update the HomeController
      if (!wasGeneralTrackingEnabled) {
        homeController.isChecked = true;
        homeController.update();
      }

      showSuccess("Started tracking for visit to ${selectedOrder!.customer}");
    } else {
      showError("Failed to start tracking. Please check location permissions.");
    }
  }

  // End tracking for the current customer visit
  Future<void> endCustomerVisitTracking() async {
    if (!isCustomerVisitTracking.value) {
      return;
    }

    final success = await locationService.endCustomerVisitTracking();

    if (success) {
      isCustomerVisitTracking.value = false;
      showSuccess("Visit tracking completed and data sent to server");
    } else {
      showError("Failed to end tracking");
    }
  }

  Future<void> make_payment() async {
    print("BTN CLICKED");
    if (paymentTextController.text.isEmpty) {
      showError("Please enter amount");
      return;
    }
    if (selectedOrder?.customer == null) {
      showError("Please select a customer");
      return;
    }
    print("home Started");

    await orderProvider
        .make_payment(
            customer: selectedOrder!.customer!,
            customer_visit: selectedOrder!.name!,
            amount: double.parse(paymentTextController.text))
        .then((result) {
      if (result.body?.message?.status?.success == true) {
        Get.back();

        showSuccess(
            result.body?.message?.status?.message ?? "Payment Successful");
      } else {
        showError((result.body?.message?.status?.message ?? "Payment Failed") +
            (result.body?.message?.getErrorList() ?? ""));
      }
    }, onError: (err) {});
  }

  Future<void> distributor_store() async {
    totalPayment = 0.0;
    try {
      final result = await orderProvider.distributor_store();
      if (result.message?.status?.success == true) {
        distributorStore = result;
        update();
      } else {
        showError((result.message?.status?.message ?? "Server Failed"));
      }

      print("distributor_store loaded");
      distributorStore?.message?.data?.itemsStock?.forEach((element) {
        print(">>>>${element.itemName}");
      });
    } catch (err) {
      showError("Server Failed");
      print(err);
    }
  }

  var totalPayment = 0.0;

  // Calculate total using shopping cart API to get accurate pricing
  Future<void> calculateTotal() async {
    print("Calculating total with shopping cart API");

    // First calculate a preliminary total using local data
    totalPayment = (distributorStore?.message?.data?.itemsStock ?? []).fold(0.0,
        (sum, product) {
      return sum +
          (product.priceListRate! *
              double.parse("${product.selectedQuantity}"));
    });

    // Update UI with preliminary total
    update();

    // Prepare items for shopping cart API
    List<Map<String, dynamic>> listofItems = [];
    distributorStore?.message?.data?.itemsStock?.forEach((element) {
      if (element.selectedQuantity > 0) {
        listofItems.add({
          "item_code": element.itemCode,
          "rate": element.priceListRate,
          "qty": element.selectedQuantity
        });
      }
    });

    if (listofItems.isEmpty) {
      // No items to calculate
      return;
    }

    if (selectedOrder?.customer == null) {
      // No customer selected yet
      return;
    }

    // Call shopping cart API to get accurate pricing
    final cartData = (await processShoppingCart(
            customer: selectedOrder!.customer, items: listofItems))
        ?.message!
        .data
        ?.cart;

    if (cartData != null) {
      // Extract the total from the cart data
      final grandTotal = cartData.total ?? 0;
      if (grandTotal != null) {
        totalPayment = double.tryParse(grandTotal.toString()) ?? totalPayment;

        // Also update the item prices in the local store data
        final updatedItems = cartData.items;
        if (updatedItems != null && updatedItems.isNotEmpty) {
          for (var updatedItem in updatedItems) {
            final itemCode = updatedItem.itemCode;
            final rate = updatedItem.rate;

            // Find and update the corresponding item in the local store
            distributorStore?.message?.data?.itemsStock?.forEach((element) {
              if (element.itemCode == itemCode) {
                element.priceListRate =
                    double.tryParse(rate.toString()) ?? element.priceListRate;
              }
            });
          }
        }
      }

      // Update UI with accurate total
      update();
    }
  }

  /*
  {
    "customer": "Hamza",
    "items": {
        "items_list": [
            {
                "item_code": "Shirt-GRE-S",
                "rate": 50,
                "qty": "1"
            },
            {
                "item_code": "Shirt-GRE-XS",
                "rate": 40,
                "qty": "2"
            }
        ]
    }
}
  * */
  // Separate function for shopping cart API call that can be called from UI
  Future<CartStore?> processShoppingCart(
      {String? customer, List<Map<String, dynamic>>? items}) async {
    if (customer == null) {
      showError("Please select a customer");
      return null;
    }

    if (items == null || items.isEmpty) {
      showError("Empty cart");
      return null;
    }

    var shoppingCartData = {
      "customer": customer,
      "items": {
        "items_list": items,
      }
    };

    try {
      // Call the shopping cart API to get updated prices and quantities

      log("$shoppingCartData");
      final shoppingCartResult =
          await orderProvider.shopping_cart(data: shoppingCartData);

      if (shoppingCartResult.message?.status?.success == true) {
        // Extract the updated cart data from the response
        return shoppingCartResult;
      } else {
        showError((shoppingCartResult.message?.status?.message ??
            "Shopping cart update failed"));
        return null;
      }
    } catch (err, ss) {
      print(ss);
      showError("Error processing shopping cart: $err");
      return null;
    }
  }

  var isInvoice = true;
  Future<void> saveInvoice({isInvoice = true}) async {
    this.isInvoice = isInvoice;
    if (selectedOrder?.customer == null) {
      showError("Please select a customer");
      return;
    }
    var listofItems = [];
    distributorStore?.message?.data?.itemsStock?.forEach((element) {
      if (element.selectedQuantity == 0) return;
      listofItems.add({
        "item_code": element.itemCode,
        "rate": element.priceListRate,
        "qty": element.selectedQuantity
      });
    });

    if (listofItems.isEmpty) {
      showError("Empty cart");
      return;
    }

    // Call the separate shopping cart processing function
    final cartData = (await processShoppingCart(
            customer: selectedOrder!.customer,
            items: List<Map<String, dynamic>>.from(listofItems)))
        ?.message
        ?.data
        ?.cart;

    if (cartData != null) {
      // Extract the updated items from the cart
      final updatedItems = cartData.items;

      if (updatedItems != null && updatedItems.isNotEmpty) {
        // Create a new list of items with updated prices and quantities
        var updatedListOfItems = [];

        for (var item in updatedItems) {
          updatedListOfItems.add({
            "item_code": item.itemCode,
            "rate": item.rate,
            "qty": isInvoice ? item.qty : (item.qty! * (-1))
          });
        }

        // Prepare data for create_invoice with updated prices and quantities
        var invoiceData = {
          if (isInvoice == false) "is_return": 1,
          "customer": "${selectedOrder!.customer}",
          "customer_visit": "${selectedOrder!.name}",
          "items": {
            "items_list": updatedListOfItems,
          }
        };

        // Call create_invoice with the updated data
        await orderProvider.create_invoice(data: invoiceData).then((result) {
          if (result.body?.message?.status?.success == true) {
            Get.back();
            showSuccess(result.body?.message?.status?.message ??
                "Invoice Payment Successful");
          } else {
            showError((result.body?.message?.status?.message ??
                    "Invoice Payment Failed") +
                (result.body?.message?.getErrorList() ?? ""));
          }
        }, onError: (err) {
          showError("Error creating invoice: $err");
        });
      } else {
        showError("No items found in the shopping cart response");
      }
    }
  }
}
