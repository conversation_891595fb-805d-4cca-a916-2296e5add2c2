import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constants.dart';
import 'package:flutter_temp/core/widgets/app_card.dart';
import 'package:get/get.dart';

import 'controllers/store_controller.dart';

class DistributorStoreScreen extends GetView<StoreController> {
  const DistributorStoreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Distributor Store'),
      ),
      body: GetBuilder<StoreController>(
        initState: (state) {
          Future.delayed(Duration.zero, () async {
            controller.loadStoreData();
          });
        },
        builder: (controller) {
          if (controller.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.distributorStore == null ||
              controller.distributorStore!.message?.data?.itemsStock == null ||
              controller.distributorStore!.message!.data!.itemsStock!.isEmpty) {
            return const Center(child: Text('No products available'));
          }

          return Column(
            children: [
              const SizedBox(height: 10),
              // Categories
              SizedBox(
                height: 50,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemCount: controller.categories.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        controller
                            .setSelectedCategory(controller.categories[index]);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 5),
                        margin: const EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          color: controller.selectedCategory ==
                                  controller.categories[index]
                              ? Constants.primaryColor
                              : Colors.grey[200],
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Center(
                          child: Text(
                            controller.categories[index],
                            style: TextStyle(
                              color: controller.selectedCategory ==
                                      controller.categories[index]
                                  ? Colors.white
                                  : Colors.black,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Products Grid
              Expanded(
                child: GridView.builder(
                  padding: const EdgeInsets.all(4),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    childAspectRatio: 0.75,
                  ),
                  itemCount: controller.filteredItems.length,
                  itemBuilder: (context, index) {
                    final product = controller.filteredItems[index];
                    return AppCard(
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Product Image
                            Expanded(
                              child: ClipRRect(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(12),
                                ),
                                child: product.itemImage != null &&
                                        product.itemImage!.isNotEmpty
                                    ? CachedNetworkImage(
                                        imageUrl: product.itemImage!,
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        placeholder: (context, url) =>
                                            Container(
                                          color: Colors.grey[200],
                                          child: const Center(
                                              child:
                                                  CircularProgressIndicator()),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Container(
                                          color: Colors.grey[200],
                                          child: const Icon(
                                              Icons.image_not_supported),
                                        ),
                                      )
                                    : Container(
                                        color: Colors.grey[200],
                                        child: const Icon(Icons.inventory,
                                            size: 50, color: Colors.grey),
                                      ),
                              ),
                            ),

                            // Product Name
                            Padding(
                              padding: const EdgeInsets.only(
                                  top: 4.0, left: 4.0, right: 4.0),
                              child: Text(
                                product.itemName ?? 'Unknown Product',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),

                            // Price
                            Padding(
                              padding: const EdgeInsets.only(
                                  top: 4.0, left: 4.0, right: 4.0),
                              child: Text(
                                '${product.priceListRate?.toStringAsFixed(2) ?? '0.00'} SAR',
                                style: const TextStyle(
                                  color: Colors.green,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),

                            // Stock Quantity
                            Padding(
                              padding: const EdgeInsets.only(
                                  top: 2.0, left: 4.0, right: 4.0, bottom: 4.0),
                              child: Text(
                                'In Stock: ${product.qtyAfterTransaction?.toInt() ?? 0}',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
