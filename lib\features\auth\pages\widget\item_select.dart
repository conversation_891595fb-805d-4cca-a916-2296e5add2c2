
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/app_font.dart';
import '../../../../core/color_app.dart';
import '../../../../core/widgets/custome_text.dart';

class ItemSelect extends StatelessWidget {
  final bool? select , showImage;
  GestureTapCallback? press;
  String ?text;
  final String ? imgUrl;
  ItemSelect({super.key, this.select, this.press, this.text, this.showImage = false, this.imgUrl});
  @override
  Widget build(BuildContext context) {

    return GestureDetector(
      onTap: press,
      child: Container(
        padding: EdgeInsets.only(top:12.h ,bottom: 12.h,right:20.w,left: 20.w),
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
                bottom: BorderSide(
                    color: ColorApp.colorGray.withOpacity(0.3), width: 0.5
                )
            )
        ),
        child: Row(
          children: [
            SizedBox(width: 10.w,),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomizeText(
                    title: "$text",fontSize: 13.sp,
                    fontFamily: Font.cairoRegular,
                  ),
                  SvgPicture.asset(
                      (select!)? "assets/images/check_select.svg": "assets/images/check_not_select.svg",
                      height: 15.h),

                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}