// lib/features/map/map_view.dart
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';

import '../home/<USER>/controllers/home_controller.dart';
import 'controllers/map_controller.dart';

class MapView extends GetView<MapViewController> {
  const MapView({super.key});

  @override
  Widget build(BuildContext context) {
    final mapController = MapController();

    if (!Get.isRegistered<MapViewController>()) {
      Get.put(MapViewController());
    }
    controller.loadData();
    return Scaffold(
      body: GetBuilder<MapViewController>(
          initState: (_) {
            Future.delayed(Duration.zero, () async {
              controller.loadLocations();
            });
          },
          builder: (controller) => controller.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : FlutterMap(
                  mapController: mapController,
                  options: MapOptions(
                    onMapReady: () {
                      if (controller.points.isNotEmpty) {
                        _zoomToPolygon(controller.points, mapController);
                      }
                    },
                    initialCenter: controller.points.isNotEmpty
                        ? controller.points[0]
                        : const LatLng(0, 0),
                    initialZoom: 13,
                  ),
                  children: [
                    TileLayer(
                      urlTemplate:
                          'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                      userAgentPackageName: 'com.example.app',
                    ),
                    if (controller.points.isNotEmpty) ...[
                      PolygonLayer(
                        polygons: [
                          Polygon(
                            points: controller.points.toList(),
                            color: Colors.blue.withOpacity(0.3),
                            borderStrokeWidth: 3,

                            // strokeWidth: 3,
                            // strokeColor: Colors.blue,

                            isFilled: true,
                          ),
                        ],
                      ),
                      MarkerLayer(
                        markers: controller.points
                            .map(
                              (point) => Marker(
                                width: 10,
                                height: 10,
                                point: point,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                      ),
                      (Get.find<HomeController>()
                                      .distributorVisits
                                      ?.message
                                      ?.data
                                      ?.customerVisits ??
                                  [])
                              .isNotEmpty
                          ? MarkerLayer(
                              markers: Get.find<HomeController>()
                                  .distributorVisits!
                                  .message!
                                  .data!
                                  .customerVisits!.where((visit) =>
                                      visit.coordinates != null &&
                                      visit.coordinates!.lat != null &&
                                      visit.coordinates!.lon != null)
                                  .map(
                                    (visit) => Marker(
                                      width: 10,
                                      height: 10,
                                      point: LatLng(visit.coordinates!.lat!,
                                          visit.coordinates!.lon!),
                                      child: Container(
                                        decoration: const BoxDecoration(
                                          color: Colors.green,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                            )
                          : const SizedBox.shrink(),
                    ],
                    const RichAttributionWidget(
                      attributions: [
                        TextSourceAttribution(
                          'OpenStreetMap contributors',
                          // onTap: () => launchUrl(Uri.parse('https://openstreetmap.org/copyright')),
                        ),
                      ],
                    ),
                  ],
                )),
    );
  }

  void _zoomToPolygon(List<LatLng> points, MapController mapController) {
    if (points.isEmpty) return;

    double minLat = points[0].latitude;
    double maxLat = points[0].latitude;
    double minLng = points[0].longitude;
    double maxLng = points[0].longitude;

    for (var point in points) {
      minLat = min(minLat, point.latitude);
      maxLat = max(maxLat, point.latitude);
      minLng = min(minLng, point.longitude);
      maxLng = max(maxLng, point.longitude);
    }

    final bounds = LatLngBounds(
      LatLng(minLat, minLng),
      LatLng(maxLat, maxLng),
    );

    mapController.move(bounds.center, 13);

    // Optional: animate to bounds
    Future.delayed(const Duration(milliseconds: 100), () {
      mapController.fitCamera(
        CameraFit.bounds(
          bounds: bounds,
          padding: const EdgeInsets.all(50),
        ),
      );
    });
  }
}
