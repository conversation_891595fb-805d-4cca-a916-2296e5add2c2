import 'package:flutter_temp/features/home/<USER>/home_provider.dart';
import 'package:flutter_temp/features/home/<USER>/models/customer_visits.dart';
import 'package:flutter_temp/features/orders/controllers/order_controller.dart';
import 'package:flutter_temp/features/orders/controllers/order_details_controller.dart';
import 'package:flutter_temp/features/orders/data/order_provider.dart';
import 'package:get/get.dart';

class OrderBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => OrderProvider());
    Get.lazyPut(() => HomeProvider());
    Get.put(OrderController(orderProvider: Get.find()));

    // Only create OrderDetailsController if we have arguments (visit data)
    if (Get.arguments != null && Get.arguments is CustomerVisits) {
      Get.put(OrderDetailsController(initialVisit: Get.arguments));
    }
  }
}
