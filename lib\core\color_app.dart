

// import 'dart:ui';

import 'package:flutter/material.dart';

class ColorApp {


  static const Color colorSeparation= Color(0xFFDADBDB);

  static const Color colorPrimary = Color(0xFF247E7B);
  static const Color colorBlue = Color(0xFF4E86FF);
  static const Color colorBadge = Color(0xFF00C9FF);
  static const Color colorText = Color(0xFF000000);
  static const Color colorBorder = Color(0xffF5F5F5);
  static const Color colorGray = Color(0xFFDDDDDD);
  static const Color colorSpace = Color(0xffD0D1D3);
  static const Color colorGray1 = Color(0xffF0F0F0);
  static const Color colorBrightGray = Color(0xffEFEFEF);
  static const Color colorBlack = Colors.black;
  static const Color colorGreen = Color(0xff00AE34);

  static const Color backgroundSplash = Color(0xFF282828);
  static const Color colorTextButtons = Color(0xFFD1D1D1);
  static const Color colorBackgroundRepyBox = Color(0xFFFAFAFA);
  static const Color colorGolden = Color.fromARGB(255, 236, 226, 29);
  static const Color colorTextSup = Color.fromARGB(255, 166, 166, 166);

  static const colorWhite = Colors.white;
}