// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:flutter_temp/core/color_app.dart';
// import 'package:flutter_temp/features/general/presentation/controllers/general_controller.dart';
//
// import 'package:webview_flutter/webview_flutter.dart';
//
// class WebPayment extends GetView<GeneralController> {
//   final String url;
//   final Function? onFinish;
//   final Function? onError;
//
//   WebPayment({required this.url, this.onFinish, this.onError});
//
//
//
//   @override
//   Widget build(BuildContext context) {
//     return
//     GetBuilder<GeneralController>(
//          init: Get.find<GeneralController>()..openUrL(url,onFinish,onError),
//
//         builder: (_) =>
//     Scaffold(
//       appBar: AppBar(
//         leading: IconButton(
//             icon: Icon(Icons.arrow_back),
//             onPressed: () {
//               Navigator.of(context).pop();
//             }),
//         backgroundColor: ColorApp.colorPrimary,
//         elevation: 0.0,
//       ),
//       body:controller.webController == null
//         ? SizedBox.shrink()
//         : WebViewWidget(
//               controller: controller.webController!,
//             ),
//     ));
//   }
// }
