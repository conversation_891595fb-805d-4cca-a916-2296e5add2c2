import 'package:get/get.dart';

import '../../../core/api_provider.dart';

class GeneralProvider extends ApiProvider {
  // Future<Response<Countries>> getCountries() => sendGet<Countries>('countries',
  //     decoder: (obj) => Countries.fromJson(obj));

  // Future<Response<Onboardings>> getOnboardings() =>
  //     loading(false).sendGet<Onboardings>('onboardings',
  //         decoder: (obj) => Onboardings.fromJson(obj));

  // Future<Response<GeneralResponseModel>> LogOut() =>
  //     loading(false).sendGet<GeneralResponseModel>('user/logout',
  //         decoder: (obj) => GeneralResponseModel.fromJson(obj),handelError: false);

  Future<Response> updateFcm(token) => loading(false).sendPost(
      'notification_tokens', {"app": "firebase", "token": token},
      retryIfFailed: false);

  Future<dynamic> authPusher(socketId, channelName) async {
    var connect = GetConnect();
    print("authPusher start   $socketId $channelName ");

//https://api.tikone.net/
    var body = (await connect.post('https://api.tikone.net/broadcasting/auth',
            {"socket_id": socketId, "channel_name": channelName},
            headers: getHeader()))
        .body;
    print("authPusher body $body");
    return body;
  }
}
