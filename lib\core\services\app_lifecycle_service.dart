// lib/core/services/app_lifecycle_service.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'native_location_service.dart';

class AppLifecycleService extends GetxService with WidgetsBindingObserver {
  final GetStorage _storage = GetStorage();

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    _checkAndResumeTracking();
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _onAppPaused();
        break;
      case AppLifecycleState.detached:
        _onAppDetached();
        break;
      case AppLifecycleState.inactive:
        // App is inactive but still visible
        break;
      case AppLifecycleState.hidden:
        // App is hidden
        break;
    }
  }

  /// Called when app is resumed from background
  void _onAppResumed() async {
    print('App resumed - checking location tracking status');
    await _checkAndResumeTracking();
  }

  /// Called when app goes to background
  void _onAppPaused() async {
    print('App paused - ensuring background services are running');
    await _ensureBackgroundServicesRunning();
  }

  /// Called when app is detached/killed
  void _onAppDetached() async {
    print('App detached - saving state');
    await _saveCurrentState();
  }

  /// Check and resume tracking if it was previously enabled
  Future<void> _checkAndResumeTracking() async {
    try {
      final shouldTrack = _storage.read('location_tracking_enabled') ?? false;

      if (shouldTrack) {
        print('App started - checking if tracking should be resumed');

        // Check if native location service is running
        final isTracking = await NativeLocationService.instance.isLocationTrackingEnabled();
        if (!isTracking) {
          print('Native location service not running, attempting to restart...');
          final success = await NativeLocationService.instance.startLocationTracking();
          if (success) {
            print('Native location service restarted');
          } else {
            print('Failed to restart native location service');
          }
        } else {
          print('Native location service is already running');
        }

        print('Background tracking check completed');
      } else {
        print('Location tracking is disabled - not resuming');
      }
    } catch (e) {
      print('Error checking and resuming tracking: $e');
    }
  }

  /// Ensure background services are running when app goes to background
  Future<void> _ensureBackgroundServicesRunning() async {
    try {
      final shouldTrack = _storage.read('location_tracking_enabled') ?? false;

      if (shouldTrack) {
        // Native service should continue running automatically
        print('App paused - native service should continue running');
      }
    } catch (e) {
      print('Error ensuring background services: $e');
    }
  }

  /// Save current state when app is being killed
  Future<void> _saveCurrentState() async {
    try {
      final isTracking = await NativeLocationService.instance.isLocationTrackingEnabled();
      await _storage.write('location_tracking_enabled', isTracking);
      await _storage.write('app_last_closed', DateTime.now().toIso8601String());
    } catch (e) {
      print('Error saving current state: $e');
    }
  }

  /// Manually start tracking
  Future<bool> startTracking() async {
    try {
      final success = await NativeLocationService.instance.startLocationTracking();
      if (success) {
        await _storage.write('location_tracking_enabled', true);
        return true;
      }
      return false;
    } catch (e) {
      print('Error starting tracking: $e');
      return false;
    }
  }

  /// Manually stop tracking
  Future<bool> stopTracking() async {
    try {
      final success = await NativeLocationService.instance.stopLocationTracking();
      if (success) {
        await _storage.write('location_tracking_enabled', false);
        return true;
      }
      return false;
    } catch (e) {
      print('Error stopping tracking: $e');
      return false;
    }
  }

  /// Check if tracking is currently enabled
  Future<bool> isTrackingEnabled() async {
    try {
      return await NativeLocationService.instance.isLocationTrackingEnabled();
    } catch (e) {
      print('Error checking tracking status: $e');
      return false;
    }
  }

  /// Get tracking status from storage
  bool getStoredTrackingStatus() {
    return _storage.read('location_tracking_enabled') ?? false;
  }
}
