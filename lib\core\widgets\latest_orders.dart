import 'package:flutter/material.dart';
import 'package:flutter_temp/core/widgets/order_card.dart';

import '../../features/home/<USER>/models/customer_visits.dart';
import '../constants.dart';

class LatestOrders extends StatelessWidget {
  final bool loading;
  final List<CustomerVisits>? customerVisits;

  // final List<Order> orders = [
  //   Order(
  //     id: 507,
  //     deliveryAddress: "New Times Square",
  //     arrivalDate: DateTime(2020, 1, 23),
  //     placedDate: DateTime(2020, 1, 17),
  //     status: OrderStatus.DELIVERING,
  //   ),
  //   Order(
  //     id: 536,
  //     deliveryAddress: "Victoria Square",
  //     arrivalDate: DateTime(2020, 1, 21),
  //     placedDate: DateTime(2020, 1, 19),
  //     status: OrderStatus.PICKING_UP,
  //   )
  // ];

  LatestOrders({super.key, required this.loading, this.customerVisits});
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 20.0,
          ),
          const Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 24.0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Latest Visits",
                  style: TextStyle(
                    color: Color.fromRGBO(19, 22, 33, 1),
                    fontSize: 18.0,
                  ),
                ),
                Text(
                  "View All",
                  style: TextStyle(
                    color: Constants.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                )
              ],
            ),
          ),
          const SizedBox(
            height: 10.0,
          ),
          if (loading)
            const Center(
              child: CircularProgressIndicator(),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 10.0,
              ),
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (BuildContext context, int index) {
                // Lets pass the order to a new widget and render it there
                return OrderCard(
                  order: customerVisits![index],
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(
                  height: 15.0,
                );
              },
              itemCount: (customerVisits??[]).length,
            )
          // Let's create an order model
        ],
      ),
    );
  }
}
