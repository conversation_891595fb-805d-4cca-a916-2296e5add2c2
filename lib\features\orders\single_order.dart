import 'package:flutter/material.dart';
import 'package:flutter_icons_null_safety/flutter_icons_null_safety.dart';
import 'package:flutter_temp/core/constants.dart';
import 'package:flutter_temp/core/widgets/app_button.dart';
import 'package:flutter_temp/features/orders/controllers/order_controller.dart';
import 'package:flutter_temp/features/orders/orders_binding.dart';
import 'package:flutter_temp/features/orders/orders_view.dart';
import 'package:get/get.dart';

import '../../core/maps_launcher.dart';
import '../../core/widgets/input_widget.dart';
import '../home/<USER>/models/customer_visits.dart';
import 'add_payment.dart';

class SingleOrder extends GetView<OrderController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderController>(

        // init: MyController(),
        initState: (_) {
          //handle order object form name navigation agruments
          if (Get.arguments is CustomerVisits) {
            controller.selectedOrder = Get.arguments;
            print("Order Selected id : ${controller.selectedOrder!.customer}");
          }
        },
        builder: (controller) => Scaffold(
              backgroundColor: Constants.primaryColor,
              body: Container(
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Positioned(
                    //   right: 0.0,
                    //   top: 10.0,
                    //   child: Opacity(
                    //     opacity: 0.3,
                    //     child: Image.asset(
                    //       // "assets/images/washing_machine_illustration.png",
                    //     ),
                    //   ),
                    // ),
                    SingleChildScrollView(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(
                              height: kToolbarHeight,
                            ),
                            GestureDetector(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: const Icon(
                                FlutterIcons.keyboard_backspace_mdi,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(
                              height: 20.0,
                            ),
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: "Details About\n",
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge!
                                        .copyWith(
                                          color: Colors.white,
                                        ),
                                  ),
                                  TextSpan(
                                    text:
                                        "Visit #${controller.selectedOrder?.name}",
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge!
                                        .copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 40.0,
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              padding: const EdgeInsets.all(16.0),
                              // height: ScreenUtil().setHeight(127.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  Text(
                                    "${controller.selectedOrder?.customer}",
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge!
                                        .copyWith(
                                          color: const Color.fromRGBO(
                                              74, 77, 84, 1),
                                          fontSize: 16.0,
                                          fontWeight: FontWeight.w800,
                                        ),
                                  ),
                                  // overdue
                                  // outstanding

                                  const SizedBox(
                                    height: 5.0,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: "Mobile: ",
                                              style: TextStyle(
                                                color: Color.fromRGBO(
                                                    143, 148, 162, 1),
                                              ),
                                            ),
                                            TextSpan(
                                              text: "0000000 \n",
                                              style: TextStyle(
                                                color: Color.fromRGBO(
                                                    74, 77, 84, 1),
                                                fontSize: 15.0,
                                              ),
                                            ),
                                            TextSpan(
                                              text: "Visit Date: ",
                                              style: TextStyle(
                                                color: Color.fromRGBO(
                                                    143, 148, 162, 1),
                                              ),
                                            ),
                                            TextSpan(
                                              text:
                                                  "${controller.selectedOrder?.visitDate} \n",
                                              style: TextStyle(
                                                color: Color.fromRGBO(
                                                    74, 77, 84, 1),
                                                fontSize: 15.0,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Image.network(
                                        "https://w7.pngwing.com/pngs/340/946/png-transparent-avatar-user-computer-icons-software-developer-avatar-child-face-heroes.png",
                                        height: 50.0,
                                        width: 50.0,
                                        fit: BoxFit.cover,
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 10.0,
                                  ),
                                  // Customer Visit Tracking Buttons
                                  Obx(() => Row(
                                        children: [
                                          Expanded(
                                            child: AppButton(
                                              text: controller
                                                      .isCustomerVisitTracking
                                                      .value
                                                  ? "End Visit Tracking"
                                                  : "Start Visit Tracking",
                                              type: controller
                                                      .isCustomerVisitTracking
                                                      .value
                                                  ? ButtonType.DANGER
                                                  : ButtonType.SUCCESS,
                                              onPressed: () {
                                                if (controller
                                                    .isCustomerVisitTracking
                                                    .value) {
                                                  controller
                                                      .endCustomerVisitTracking();
                                                } else {
                                                  controller
                                                      .startCustomerVisitTracking();
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      )),
                                  const SizedBox(
                                    height: 10.0,
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: AppButton(
                                          text: "Add Invoice",
                                          type: ButtonType.PLAIN,
                                          onPressed: () {
                                            Get.to(AddPaymentScreen(
                                              isInvoice: true,
                                            ));
                                          },
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Expanded(
                                          child: AppButton(
                                        text: "Visits History",
                                        type: ButtonType.PLAIN,
                                        onPressed: () {
                                          Get.to( OrdersScreen( controller.selectedOrder?.customer ?? ""), binding: OrdersBinding());
                                        },
                                      )),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 10.0,
                                  ),

                                  Row(
                                    children: [
                                      Expanded(
                                          child: AppButton(
                                        text: "Add Payment",
                                        type: ButtonType.PLAIN,
                                        onPressed: () {
                                          Get.bottomSheet(Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Container(
                                                width: double.infinity,
                                                constraints: const BoxConstraints(
                                                    // minHeight:
                                                    // MediaQuery.of(context).size.height - 180.0,
                                                    ),
                                                decoration: const BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.only(
                                                    topLeft:
                                                        Radius.circular(30.0),
                                                    topRight:
                                                        Radius.circular(30.0),
                                                  ),
                                                  color: Colors.white,
                                                ),
                                                padding:
                                                    const EdgeInsets.all(24.0),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment
                                                          .stretch,
                                                  children: [
                                                    // Lets make a generic input widget
                                                    InputWidget(
                                                      topLabel: "Amount",
                                                      mumber: true,
                                                      controller: controller
                                                          .paymentTextController,
                                                      hintText:
                                                          "Enter value of Amount",
                                                    ),

                                                    const SizedBox(
                                                      height: 15.0,
                                                    ),

                                                    AppButton(
                                                      type: ButtonType.PRIMARY,
                                                      text: "Add Payment",
                                                      onPressed: controller
                                                          .make_payment,
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ));
                                        },
                                      )),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Expanded(
                                          child: AppButton(
                                        text: "Add Refund",
                                        type: ButtonType.PLAIN,
                                        onPressed: () {
                                          Get.to(AddPaymentScreen(
                                            isInvoice: false,
                                          ));

                                          // Get.bottomSheet(Column(
                                          //   mainAxisSize: MainAxisSize.min,
                                          //   children: [
                                          //     Container(
                                          //       width: double.infinity,
                                          //       constraints: const BoxConstraints(
                                          //           // minHeight:
                                          //           // MediaQuery.of(context).size.height - 180.0,
                                          //           ),
                                          //       decoration: const BoxDecoration(
                                          //         borderRadius:
                                          //             BorderRadius.only(
                                          //           topLeft:
                                          //               Radius.circular(30.0),
                                          //           topRight:
                                          //               Radius.circular(30.0),
                                          //         ),
                                          //         color: Colors.white,
                                          //       ),
                                          //       padding:
                                          //           const EdgeInsets.all(24.0),
                                          //       child: Column(
                                          //         crossAxisAlignment:
                                          //             CrossAxisAlignment
                                          //                 .stretch,
                                          //         children: [
                                          //           // Lets make a generic input widget
                                          //           InputWidget(
                                          //             topLabel: "Amount",
                                          //             controller: controller
                                          //                 .refoudTextController,
                                          //                 mumber: true  ,
                                          //             hintText:
                                          //                 "Enter value of Amount",
                                          //           ),

                                          //           const SizedBox(
                                          //             height: 15.0,
                                          //           ),

                                          //           AppButton(
                                          //             type: ButtonType.PRIMARY,
                                          //             text: "Add Refund",
                                          //             onPressed: () {
                                          //               // onPressed: controller
                                          //               //   .make_payment,
                                          //               // Get.offAllNamed(Routes.dashboard);
                                          //             },
                                          //           )
                                          //         ],
                                          //       ),
                                          //     ),
                                          //   ],
                                          // ));
                                        },
                                      )),
                                    ],
                                  ),

                                  const SizedBox(
                                    height: 15.0,
                                  ),

                                  AppButton(
                                    text: "View Location",
                                    type: ButtonType.PRIMARY,
                                    onPressed: () async {
                                      MapsLauncher.openMap(
                                          controller
                                              .selectedOrder!.coordinates!.lat!,
                                          controller.selectedOrder!.coordinates!
                                              .lon!);
                                    },
                                  )
                                ],
                              ),
                            ),
                            const SizedBox(height: 10.0),
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8.0),
                                color: Constants.scaffoldBackgroundColor,
                              ),
                              padding: const EdgeInsets.symmetric(
                                vertical: 24.0,
                                horizontal: 16.0,
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Financial ",
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge!
                                        .copyWith(
                                          color: const Color.fromRGBO(
                                              74, 77, 84, 1),
                                          fontSize: 18.0,
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                  const SizedBox(
                                    height: 10.0,
                                  ),
                                  getItemRow("Overdue", "*",
                                      "\$${controller.selectedOrder?.creditLimit}"),
                                  getItemRow("Outstanding", "",
                                      "\$${controller.selectedOrder?.outstanding}"),
                                  const SizedBox(
                                    height: 30.0,
                                  ),
                                  const SizedBox(
                                    height: 10.0,
                                  ),
                                  getTotalRow("Total",
                                      "\$${controller.selectedOrder?.balance}"),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }
}

Widget getTotalRow(String title, String amount) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    child: Row(
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Color.fromRGBO(19, 22, 33, 1),
            fontSize: 17.0,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        Text(
          amount,
          style: const TextStyle(
            color: Constants.primaryColor,
            fontWeight: FontWeight.w600,
            fontSize: 17.0,
          ),
        )
      ],
    ),
  );
}

Widget getSubtotalRow(String title, String price) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    child: Row(
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Color.fromRGBO(74, 77, 84, 1),
            fontSize: 15.0,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        Text(
          price,
          style: const TextStyle(
            color: Color.fromRGBO(74, 77, 84, 1),
            fontSize: 15.0,
          ),
        )
      ],
    ),
  );
}

Widget getItemRow(String count, String item, String price) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 8.0),
    child: Row(
      children: [
        Text(
          count,
          style: const TextStyle(
            color: Color.fromRGBO(74, 77, 84, 1),
            fontSize: 15.0,
            fontWeight: FontWeight.w600,
          ),
        ),
        Expanded(
          child: Text(
            " $item",
            style: const TextStyle(
              color: Color.fromRGBO(143, 148, 162, 1),
              fontSize: 15.0,
            ),
          ),
        ),
        Text(
          price,
          style: const TextStyle(
            color: Color.fromRGBO(74, 77, 84, 1),
            fontSize: 15.0,
          ),
        )
      ],
    ),
  );
}
