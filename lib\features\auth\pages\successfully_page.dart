import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class SuccessfullyPage extends StatefulWidget {
  const SuccessfullyPage({super.key});

  @override
  _SuccessfullyPageState createState() => _SuccessfullyPageState();
}

class _SuccessfullyPageState extends State<SuccessfullyPage> {
  bool isLoading = false;
  loginUser(BuildContext context) {
    setState(() {
      isLoading = false;
    });
    try {
      // Get.off(HomeScreen());
    } catch (err) {
      setState(() {
        isLoading = false;
      });
      print(err);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      body: Stack(
        fit: StackFit.expand,
        children: [
          Positioned(
              left: 0,
              top: 0,
              width: 200,
              height: 200,
              child: SvgPicture.asset(
                "assets/images/ci_top.svg",
                fit: BoxFit.fill,
              )),
          Positioned(
            right: 0,
            bottom: 0,
            height: 322,
            width: 142,
            child: SvgPicture.asset(
              "assets/images/ci_botom.svg",
              fit: BoxFit.fill,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: SingleChildScrollView(
              child: Container(
                alignment: Alignment.center,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 3),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: SafeArea(
                            child: Container(
                              margin: const EdgeInsetsDirectional.only(start: 12),
                              child: const Icon(
                                Icons.arrow_back_ios,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 110.h),
                    SvgPicture.asset(
                      "assets/images/Confirmed-rafiki.svg",
                      height: 260.h,
                      width: 195.w,
                    ),
                    SizedBox(height: 40.h),
                    const Text(
                      "Congratulations",
              
                    ),
                    SizedBox(height: 25.h),
                    const Text(
                      "Your Registration process is \n successfully completed",
                     
                    ),
                    SizedBox(
                      height: 70.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () => loginUser(context),
                          child: Center(
                            child: isLoading == false
                                ? Row(
                                    children: [
                                      const Text(
                                        "Sign In",
                                     
                                      ),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      SvgPicture.asset(
                                        "assets/images/btn_auth.svg",
                                      )
                                    ],
                                  )
                                : const CircularProgressIndicator(
                                    backgroundColor: Colors.white,
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
