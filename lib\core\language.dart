import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_temp/features/general/presentation/controllers/general_controller.dart';
import 'package:get/get.dart';

import '../main.dart';

class DropdownButtonWidget extends StatefulWidget {
  const DropdownButtonWidget({super.key});

  @override
  _DropdownButtonWidgetState createState() => _DropdownButtonWidgetState();
}

class _DropdownButtonWidgetState extends State<DropdownButtonWidget> {
  Language? _selectedLanguage;
  final List<Language> _languages = [
    Language('en', 'English', Icons.language, 'EN'),
    Language('ar', 'ِالعربية', Icons.language, 'AR'),

    // Add more languages and icons as needed
  ];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 100.w, // Set the desired width of the dropdown button
      height: 30.h,

      // decoration: BoxDecoration(
      //   border: Border.all(
      //     color: Color(0xFFC6C6C6),
      //     // Adjust the radius as needed
      //   ),
      //   borderRadius: BorderRadius.circular(8),
      // ),
      child: DropdownButtonFormField<Language>(
        value: _selectedLanguage,
        icon: const Icon(
          Icons.keyboard_arrow_down_rounded,
          size: 17,
        ),
        // isExpanded: true,
        decoration: InputDecoration(
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Color(0xFFC6C6C6),
            ),
            borderRadius: BorderRadius.circular(8.0.w),
          ),

          border: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Color(0xFFC6C6C6),
            ),
            borderRadius: BorderRadius.circular(8.0.w),
            // Adjust the radius as needed
          ),

          contentPadding: const EdgeInsets.all(8),

          filled: true,
          fillColor: Colors.white, // Background color of the button
        ),
        // iconSize: 16,

        // style: TextStyle(fontSize: 8.sp, color: Colors.black),

        onChanged: (value) {
          setState(() {
            _selectedLanguage = value;
            // LocalizationService.changeLocale(
            //     Locale(value!.code, value!.countryCode));

            Get.find<GeneralController>()
                .changeLang(Locale(value!.code, value.countryCode));
          });
        },

        items: _languages.map((Language language) {
          return DropdownMenuItem<Language>(
            value: language,
            child: Row(
              children: <Widget>[
                Icon(
                  language.icon,
                  size: 15.w,
                  color: const Color(0xFF000000),
                ),
                SizedBox(width: 5.w), // Add some spacing between icon and text
                Text(language.name,
                    style: TextStyle(
                        fontSize: 12.sp, fontWeight: FontWeight.w500)),
              ],
            ),
          );
        }).toList(),
        hint: Row(
          children: <Widget>[
            Icon(
              _languages[0].icon,
              size: 15,
              color: const Color(0xFF000000),
            ),
            SizedBox(width: 5.w), // Add some spacing between icon and text
            Text(
                _languages[_languages.indexWhere((element) => element.code == box.read("lang"))].name,
                style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500)),
          ],
        ),
        // Placeholder text
      ),
    );
  }
}

class Language {
  final String code;
  final String name;
  final String countryCode;
  final IconData icon;

  Language(this.code, this.name, this.icon, this.countryCode);
}
