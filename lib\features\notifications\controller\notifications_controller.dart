// // import 'package:firebase_auth/firebase_auth.dart';
// import 'package:dash_chat_2/dash_chat_2.dart';
// import 'package:get/get.dart';
//
// import 'package:flutter_temp/features/inbox/data/chat_details.dart';
// import 'package:flutter_temp/features/inbox/data/chats.dart';
// import 'package:flutter_temp/features/inbox/inbox_provider.dart';
// import 'package:flutter_temp/features/notifications/data/notifications.dart';
// import 'package:flutter_temp/features/streams/model/streams.dart';
// import '../../general/presentation/controllers/general_controller.dart';
// import '../../streams/model/stream_details.dart';
// import '../../streams/stream_provider.dart';
// import '../notifications_provider.dart';
// // import '../../../constants/api_maneger.dart';
//
// class NotificationsController extends GetxController {
//   NotificationsController(
//       this.provider, this.inboxProvider, this.streamProvider);
//   final NotificationsProvider provider;
//   final InboxProvider inboxProvider;
//   final StreamProvider streamProvider;
//   Chats? chats;
//   List<StreamDetailsData> streams = [];
//   List<NotificationsData> notifications = [];
//
//   @override
//   void onReady() {
//     // authProvider
//     //     .getCountries()
//     //     .then((value) => countries = value.body?.data ?? []);
//   }
//
//   loadData() {
//     getNotifications();
//     getConversations();
//     getStreams();
//   }
//
//   var generalController = Get.put(GeneralController());
//
//   var isLoadingNotifications = false;
//
//   getNotifications() async {
//     isLoadingNotifications = true;
//     updateIFNeed();
//     var _ = await provider.getNotifications();
//     if (_.isOk) {
//       notifications = _.body?.data ?? [];
//       updateIFNeed();
//     }
//     isLoadingNotifications = false;
//     updateIFNeed();
//   }
//   var isLoadingConversations = false;
//
//   getConversations() async {
//     isLoadingConversations = true;
//     updateIFNeed();
//     var _ = await provider.getChats();
//     if (_.isOk) {
//       chats = _.body;
//       updateIFNeed();
//     }
//     isLoadingConversations = false;
//     updateIFNeed();
//   }
//
//   var isLoadingStreams = false;
//   Future<void> getStreams({dynamic page = 1}) async {
//     isLoadingStreams = true;
//     updateIFNeed();
//
//     var res = await streamProvider.streams(page: page);
//     if (res.isOk) {
//       if (page == 1) {
//         streams = res.body?.data ?? [];
//       } else {
//         streams.addAll(res.body?.data ?? []);
//       }
//     }
//     isLoadingStreams = false;
//     updateIFNeed();
//   }
// }
