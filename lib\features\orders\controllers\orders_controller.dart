import 'package:get/get.dart';

import '../../../core/constant.dart';
import '../../home/<USER>/home_provider.dart';
import '../../home/<USER>/models/customer_visits.dart';

class OrdersController extends GetxController {
  final HomeProvider homeProvider;

  OrdersController({required this.homeProvider});

  // Observable variables
  RxBool isLoading = false.obs;
  Rx<DistributorVisits?> distributorVisits = Rx<DistributorVisits?>(null);
  RxString customerFilter = "".obs;

  @override
  void onInit() {
    super.onInit();
  }

  // Fetch customer visits from the API
  Future<void> fetchCustomerVisits(customer) async {
    try {
      isLoading.value = true;
      update();

      final response = await homeProvider.getCustomerVisitts(customer);

      if (response != null) {
        distributorVisits.value = response;
        update();
      } else {
        showError("Failed to load customer visits");
      }
    } catch (e) {
      print(e);
      showError("Error: ${e.toString()}");
    } finally {
      isLoading.value = false;
    }
    update();
  }

  // Get filtered customer visits
  List<CustomerVisits> get filteredVisits {
    return distributorVisits.value?.message?.data?.customerVisits ?? [];
  }
}
