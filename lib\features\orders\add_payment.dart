import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constants.dart';
import 'package:get/get.dart';

import '../../core/widgets/app_button.dart';
import 'controllers/order_controller.dart';

class AddPaymentScreen extends GetView<OrderController> {
  final bool isInvoice;
  AddPaymentScreen({super.key, required this.isInvoice});

  String searchQuery = '';
  // List<Map<String, dynamic>> displayedProducts = [];

  void filterProducts() {
    controller.update();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderController>(

        // init: MyController(),
        initState: (_) {
          //handle order object form name navigation agruments
          Future.delayed(Duration.zero, () async {
            if (_.controller!.distributorStore == null) {
              await _.controller!.distributor_store();
            }
            // Calculate total with shopping cart API to get accurate pricing
            await _.controller!.calculateTotal();
          });
        },
        builder: (controller) => Scaffold(
              appBar: AppBar(
                title: Text(isInvoice ? "Add Invoice" : "Add Refund"),
                backgroundColor: Constants.primaryColor,
              ),
              body: Column(
                children: [
                  // Search Bar
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextField(
                      onChanged: (value) {
                        searchQuery = value;
                        filterProducts();
                      },
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.grey[200],
                        hintText: 'Search for a product',
                        prefixIcon: const Icon(Icons.search,
                            color: Constants.primaryColor),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                  // Categories List
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: controller.distributorStore?.message?.data
                              ?.itemsStock?.length ??
                          0,
                      itemBuilder: (context, index) {
                        final product = controller.distributorStore!.message!
                            .data!.itemsStock![index];
                        return (searchQuery.trim().isEmpty ||
                                product.itemName!
                                    .toLowerCase()
                                    .contains(searchQuery.toLowerCase()))
                            ? Card(
                                margin: const EdgeInsets.symmetric(vertical: 8),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                elevation: 5,
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      // Product Image Placeholder
                                      Container(
                                        height: 60,
                                        width: 60,
                                        decoration: BoxDecoration(
                                          color: Colors.grey[300],
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        child: const Icon(Icons.image,
                                            color: Constants.primaryColor,
                                            size: 30),
                                      ),
                                      const SizedBox(width: 16),
                                      // Product Details
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              product.itemName ?? "-",
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 18,
                                              ),
                                            ),
                                            Text(
                                              '\$${product.priceListRate ?? "-"}',
                                              style: const TextStyle(
                                                  color: Colors.green,
                                                  fontSize: 16),
                                            ),
                                            Text(
                                              'Available: ${product.qtyAfterTransaction ?? "-"}',
                                              style: const TextStyle(
                                                  color: Colors.grey),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // Quantity Selector
                                      Row(
                                        children: [
                                          IconButton(
                                            icon: const Icon(
                                                Icons.remove_circle,
                                                color: Colors.red),
                                            onPressed:
                                                product.selectedQuantity > 0
                                                    ? () async {
                                                        product
                                                            .selectedQuantity--;

                                                        await controller
                                                            .calculateTotal();
                                                      }
                                                    : null,
                                          ),
                                          Text(
                                            '${product.selectedQuantity}',
                                            style:
                                                const TextStyle(fontSize: 16),
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.add_circle,
                                                color: Colors.green),
                                            onPressed: product
                                                        .selectedQuantity <
                                                    (product.qtyAfterTransaction ??
                                                        0)
                                                ? () async {
                                                    product.selectedQuantity++;

                                                    await controller
                                                        .calculateTotal();
                                                  }
                                                : null,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            : Container();
                      },
                    ),
                  ),
                  // Total Payment and Save Button
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Total Payment:',
                              style: TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                            Text(
                              '\$${controller.totalPayment.toStringAsFixed(2)}',
                              style: const TextStyle(
                                  fontSize: 20, color: Constants.primaryColor),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        AppButton(
                          type: controller.totalPayment > 0
                              ? ButtonType.PRIMARY
                              : ButtonType.PLAIN,
                          text: isInvoice ? "Save Invoice" : "Save Refund",
                          onPressed: () =>
                              controller.saveInvoice(isInvoice: isInvoice),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ));
  }
}
