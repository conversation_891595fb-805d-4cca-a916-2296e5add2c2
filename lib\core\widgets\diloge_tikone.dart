import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// ignore: must_be_immutable
class TikOneDialog extends StatelessWidget {

  String title;
  String content;
  VoidCallback continueCallBack;

  TikOneDialog(this.title, this.content, this.continueCallBack, {super.key});
  TextStyle textStyle = const TextStyle (color: Colors.black);

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
        child:  AlertDialog(
          title:  Text(title.tr,style: textStyle,),
          content:  Text(content.tr, style: textStyle,),
          actions: <Widget>[
             ElevatedButton(
              onPressed:continueCallBack,
              child:  Text("ok".tr),
            ),
             ElevatedButton(
              child: Text("Cancel".tr),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        ));
  }
}