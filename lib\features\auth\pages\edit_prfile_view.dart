import 'package:flutter/material.dart';
import 'package:flutter_temp/core/widgets/app_bar.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

import '../../../core/color_app.dart';
import '../../../core/widgets/app_card.dart';
import '../../general/presentation/controllers/general_controller.dart';
import '../controller/auth_controller.dart';

class EditProfileView extends GetView<AuthController> {
  const EditProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        // backgroundColor: ColorApp.colorbackground,
        appBar: getAppbarWithLogo("account".tr, canBack: false),
        resizeToAvoidBottomInset: false,
        body: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppCard(
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 35,
                      backgroundImage: AssetImage('assets/profile_image.jpg'),
                    ),
                    SizedBox(width: 16.0),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${Get.find<GeneralController>().currentUser?.employeeId}',
                          style: TextStyle(
                            fontSize: 20.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'N/A',
                          style: TextStyle(
                            fontSize: 16.0,
                            color: ColorApp.colorTextSup,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Gap(16),
              AppCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 8.0),
                    Text(
                      'Employee ID',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.0),
                    Text(
                        '${Get.find<GeneralController>().currentUser?.employeeId}',
                        style: TextStyle(
                          fontSize: 16.0,
                          color: ColorApp.colorTextSup,
                          fontWeight: FontWeight.normal,
                        )),
                    Divider(
                      color: ColorApp.colorTextSup,
                      height: 10,
                      thickness: 0.5,
                    ),
                    Text(
                      'Date of Joining',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.0),
                    Text(
                        '${Get.find<GeneralController>().currentUser?.dateOfJoining}',
                        style: TextStyle(
                          fontSize: 16.0,
                          color: ColorApp.colorTextSup,
                          fontWeight: FontWeight.normal,
                        )),
                    Divider(
                      color: ColorApp.colorTextSup,
                      height: 10,
                      thickness: 0.5,
                    ),
                    SizedBox(height: 16.0),
                    Text(
                      'Official email address',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.0),
                    Text(
                        '${Get.find<GeneralController>().currentUser?.companyEmail ?? "n/a"}',
                        style: TextStyle(
                          fontSize: 16.0,
                          color: ColorApp.colorTextSup,
                          fontWeight: FontWeight.normal,
                        )),
                    Divider(
                      color: ColorApp.colorTextSup,
                      height: 10,
                      thickness: 0.5,
                    ),
                    Text(
                      'Contact number',
                      style: TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.0),
                    Text(
                        '${Get.find<GeneralController>().currentUser?.contactNumber ?? "n/a"}',
                        style: TextStyle(
                          fontSize: 16.0,
                          color: ColorApp.colorTextSup,
                          fontWeight: FontWeight.normal,
                        )),
                  ],
                ),
              ),
              // Gap(16),
              // AppCard(
              //   child: Column(
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       GestureDetector(
              //           onTap: () {
              //             // Get.to(PersonalDetailsScreen());
              //           },
              //           behavior: HitTestBehavior.translucent,
              //           child: Row(
              //             children: [
              //               Text(
              //                 'Personal Details',
              //                 style: TextStyle(
              //                   fontSize: 16.0,
              //                   fontWeight: FontWeight.w500,
              //                 ),
              //                 // go icon
              //               ),
              //               const Spacer(),
              //               //icon go
              //               Icon(
              //                 Icons.arrow_forward_ios_rounded,
              //                 color: ColorApp.colorTextSup,
              //                 size: 16,
              //               ),
              //             ],
              //           )),
              //       Divider(
              //         color: ColorApp.colorTextSup,
              //         height: 16,
              //         thickness: 0.5,
              //       ),
              //
              //
              //     ],
              //   ),
              // ),
            ],
          ),
        ));
  }
}
