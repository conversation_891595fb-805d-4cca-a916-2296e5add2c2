import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constant.dart';
// import 'package:video_thumbnail/video_thumbnail.dart';

class VideoCard extends StatelessWidget {
  final String? counter;
  final String? videoUrl;
  const VideoCard({super.key, this.counter, this.videoUrl});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Positioned(
            top: 0,
            right: 1,
            left: 2,
            bottom: 0,
            child: FadeInImage(
                height: 50,
                width: 50,
                fadeInDuration: const Duration(milliseconds: 500),
                fadeInCurve: Curves.easeInExpo,
                fadeOutCurve: Curves.easeOutExpo,
                placeholder:
                    const NetworkImage(placeholder), //AssetImage(placeholder),
                image: NetworkImage(videoUrl!),
                imageErrorBuilder: (context, error, stackTrace) {
                  return Container(
                    child: const Icon(Icons.error_outline),
                  );
                },
                fit: BoxFit.cover),
          ),

          // Container(
          //   child: FutureBuilder<Uint8List?>(
          //     future: VideoThumbnail.thumbnailData(
          //       video: videoUrl!,
          //       imageFormat: ImageFormat.JPEG,
          //       quality: 25,
          //     ),
          //     builder: (context, snapshot) {
          //       if (snapshot.hasError) {
          //         print("error d ${snapshot.error}");
          //             return Center(child: Icon(Icons.error_outline),);
          //       }
          //       else if (snapshot.hasData && snapshot.data != null) {
          //         return Image.memory(
          //           snapshot.data!,
          //           fit: BoxFit.cover,
          //         );
          //       }
          //        else {
          //         return Container(
          //           child: LoadingIndicator(),
          //         );
          //       }
          //     },
          //   ),
          // ),

          //),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                counter ?? "",
              ),
              const Icon(
                Icons.play_arrow_outlined,
                color: Colors.white,
                size: 22,
              )
            ],
          ),
        ],
      ),
    );
  }
}
