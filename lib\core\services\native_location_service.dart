// lib/core/services/native_location_service.dart
import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../features/general/presentation/controllers/general_controller.dart';
import '../constant.dart';
import 'location_database.dart';

class NativeLocationService {
  static const MethodChannel _channel = MethodChannel('com.temp.flutter_temp/location_service');
  final _dio = Dio(); // Initialize Dio instance

  // Singleton pattern
  static NativeLocationService? _instance;
  static NativeLocationService get instance {
    _instance ??= NativeLocationService._internal();
    return _instance!;
  }

  NativeLocationService._internal() {
    _setupMethodCallHandler();
  }

  // Track if we're currently tracking a customer visit
  String? _activeCustomerVisitId;
  String? _activeCustomerName;


  
  // Stream controller for location updates
  final StreamController<Map<String, dynamic>> _locationController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  Stream<Map<String, dynamic>> get locationStream => _locationController.stream;
  

  
  void _setupMethodCallHandler() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onLocationUpdate':
          final Map<String, dynamic> locationData = Map<String, dynamic>.from(call.arguments);
          await _handleLocationUpdate(locationData);
          break;
        case 'onPermissionResult':
          final bool granted = call.arguments['granted'] ?? false;
          print('Permission result: $granted');
          break;
        case 'onTrackingStatusChanged':
          final bool isTracking = call.arguments['isTracking'] ?? false;
          print('Tracking status changed: $isTracking');
          if (!isTracking) {
            // Update local storage when tracking is stopped from notification
            final box = GetStorage();
            await box.write('location_tracking_enabled', false);
          }
          break;
        case 'requestSync':
          print('🔄 Sync requested from native service');
          await _syncLocationsToApi();
          break;
        default:
          print('Unknown method call: ${call.method}');
      }
    });
  }
  
  Future<void> _handleLocationUpdate(Map<String, dynamic> locationData) async {
    try {
      print('Received location update: ${locationData['latitude']}, ${locationData['longitude']}');
      
      // Save to local database
      await saveLocationData(
        latitude: locationData['latitude'],
        longitude: locationData['longitude'],
        timestamp: DateTime.fromMillisecondsSinceEpoch(locationData['timestamp']).toIso8601String(),
      );
      
      // Emit to stream for any listeners
      _locationController.add(locationData);
      
    } catch (e) {
      print('Error handling location update: $e');
    }
  }
  
  /// Check if location permissions are granted
  Future<bool> hasLocationPermissions() async {
    try {
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod('hasLocationPermissions');
        return result ?? false;
      }
      return false;
    } catch (e) {
      print('Error checking location permissions: $e');
      return false;
    }
  }
  
  /// Request location permissions
  Future<bool> requestLocationPermissions() async {
    try {
      // First check using permission_handler
      final locationPermission = await Permission.location.request();
      final locationAlwaysPermission = await Permission.locationAlways.request();
      
      if (locationPermission.isGranted && locationAlwaysPermission.isGranted) {
        return true;
      }
      
      // If not granted, show settings
      if (locationPermission.isPermanentlyDenied || locationAlwaysPermission.isPermanentlyDenied) {
        await openAppSettings();
      }
      
      return false;
    } catch (e) {
      print('Error requesting location permissions: $e');
      return false;
    }
  }
  
  /// Start location tracking
  Future<bool> startLocationTracking() async {
    try {
      // Check permissions first
      if (!await hasLocationPermissions()) {
        final granted = await requestLocationPermissions();
        if (!granted) {
          print('Location permissions not granted');
          return false;
        }
      }
      
      final result = await _channel.invokeMethod('startLocationTracking');
      
      if (result == true) {
        // Save tracking state
        final box = GetStorage();
        await box.write('location_tracking_enabled', true);
        print('Native location tracking started successfully');
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error starting location tracking: $e');
      return false;
    }
  }
  
  /// Stop location tracking
  Future<bool> stopLocationTracking() async {
    try {
      final result = await _channel.invokeMethod('stopLocationTracking');
      
      if (result == true) {
        // Save tracking state
        final box = GetStorage();
        await box.write('location_tracking_enabled', false);
        print('Native location tracking stopped successfully');
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error stopping location tracking: $e');
      return false;
    }
  }
  
  /// Check if location tracking is currently enabled
  Future<bool> isLocationTrackingEnabled() async {
    try {
      final result = await _channel.invokeMethod('isLocationTrackingEnabled');
      return result ?? false;
    } catch (e) {
      print('Error checking location tracking status: $e');
      return false;
    }
  }
  
  /// Start tracking for a specific customer visit
  Future<void> startCustomerVisitTracking(String customerId, String customerName) async {
    try {
      _activeCustomerVisitId = customerId;
      _activeCustomerName = customerName;
      
      // Save customer visit to database
      final db = LocationDatabase.instance;
      await db.startCustomerVisit(customerId, customerName);
      
      // Start location tracking if not already running
      final isTracking = await isLocationTrackingEnabled();
      if (!isTracking) {
        await startLocationTracking();
      }
      
      print('Started customer visit tracking for: $customerName');
    } catch (e) {
      print('Error starting customer visit tracking: $e');
    }
  }
  
  /// Stop tracking for the current customer visit
  Future<void> stopCustomerVisitTracking() async {
    try {
      if (_activeCustomerVisitId != null) {
        // End customer visit in database
        final db = LocationDatabase.instance;
        await db.endCustomerVisit(_activeCustomerVisitId!);
        
        print('Stopped customer visit tracking for: $_activeCustomerName');
        
        _activeCustomerVisitId = null;
        _activeCustomerName = null;
      }
    } catch (e) {
      print('Error stopping customer visit tracking: $e');
    }
  }
  
  /// Save location data to local database
  Future<void> saveLocationData({
    required double latitude,
    required double longitude,
    required String timestamp,
  }) async {
    try {
      final locationData = {
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': timestamp,
        'customer_visit': _activeCustomerVisitId,
      };
      
      final db = LocationDatabase.instance;
      await db.insert(locationData);
      
      print('Location saved: $latitude, $longitude at $timestamp');
    } catch (e) {
      print('Error saving location data: $e');
    }
  }
  
  /// Get all stored locations
  Future<List<Map<String, dynamic>>> getAllLocations() async {
    try {
      final db = LocationDatabase.instance;
      return await db.getAllLocations();
    } catch (e) {
      print('Error getting all locations: $e');
      return [];
    }
  }
  
  /// Get locations for a specific customer visit
  Future<List<Map<String, dynamic>>> getCustomerVisitLocations(String visitId) async {
    try {
      final db = LocationDatabase.instance;
      return await db.getCustomerVisitLocations(visitId);
    } catch (e) {
      print('Error getting customer visit locations: $e');
      return [];
    }
  }
  
  /// Clear all stored locations
  Future<void> clearAllLocations() async {
    try {
      final db = LocationDatabase.instance;
      await db.deleteAllLocations();
      print('All locations cleared');
    } catch (e) {
      print('Error clearing locations: $e');
    }
  }
  
  /// Get current active customer visit
  String? get activeCustomerVisitId => _activeCustomerVisitId;
  String? get activeCustomerName => _activeCustomerName;
  
  /// Sync locations to API
  Future<void> _syncLocationsToApi() async {
    try {
      print('🔄 Starting location sync...');

      // Get all locations from database
      final locations = await LocationDatabase.instance.getAllLocations();
      print('📍 Found ${locations.length} locations to sync');

      if (locations.isEmpty) {
        print('✅ No locations to sync');
        return;
      }

      // Transform locations to required format
      final coordinates = locations
          .map((loc) => {
                "lat": loc['latitude'],
                "lon": loc['longitude'],
                "timestamp": loc['timestamp'],
              })
          .toList();

      final data = {"group": "group", "coordinates": coordinates};

      // Get token
      final token = Get.find<GeneralController>().token();
      print('🔑 Token available: ${token != null}');

      if (token == null) {
        print('❌ No authentication token available');
        return;
      }

      print('🌐 Sending ${coordinates.length} coordinates to API...');
      print('📡 API URL: ${baseApiUrl}rukn_distributor.api.distributor_tracking');

      final response = await _dio.post(
        '${baseApiUrl}rukn_distributor.api.distributor_tracking',
        options: Options(
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json'
          },
        ),
        data: data,
      );

      print('📨 API Response: ${response.statusCode}');
      print('📄 Response data: ${response.data}');

      if (response.statusCode == 200) {
        // Clear local database after successful sync
        await LocationDatabase.instance.deleteAllLocations();
        print('✅ Locations synced successfully and cleared from local DB');
      } else {
        print('❌ Failed to sync locations: ${response.statusMessage}');
      }
    } catch (e) {
      print('❌ Error syncing locations: $e');
      if (e is DioException) {
        print('🌐 Dio Error Details:');
        print('   Status Code: ${e.response?.statusCode}');
        print('   Response Data: ${e.response?.data}');
        print('   Error Message: ${e.message}');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _locationController.close();
  }
}
