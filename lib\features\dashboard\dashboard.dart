import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_icons_null_safety/flutter_icons_null_safety.dart';
import 'package:flutter_temp/core/constants.dart';
import 'package:flutter_temp/features/auth/pages/edit_prfile_view.dart';
import 'package:flutter_temp/features/map/map_view.dart';
import 'package:flutter_temp/features/store/store_view.dart';

import '../home/<USER>/views/home_view.dart';


class MainTab extends StatefulWidget {
  const MainTab({super.key});

  @override
  _MainTabState createState() => _MainTabState();
}

class _MainTabState extends State<MainTab> {
  // Track active index
  int activeIndex = 0;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: CurvedNavigationBar(
        backgroundColor: Constants.scaffoldBackgroundColor,
        buttonBackgroundColor: Constants.primaryColor,
        items: [
          Icon(
            FlutterIcons.ios_home_ion,
            size: 30.0,
            color: activeIndex == 0 ? Colors.white : const Color(0xFFC8C9CB),
          ),
          Icon(
            FlutterIcons.map_marker_radius_mco,
            size: 30.0,
            color: activeIndex == 1 ? Colors.white : const Color(0xFFC8C9CB),
          ),
          // Icon(
          //   FlutterIcons.plus_ant,
          //   size: 30.0,
          //   color: activeIndex == 2 ? Colors.white : Color(0xFFC8C9CB),
          // ),
          Icon(
            FlutterIcons.heart_fea,
            size: 30.0,
            color: activeIndex == 2 ? Colors.white : const Color(0xFFC8C9CB),
          ),
          Icon(
            FlutterIcons.setting_ant,
            size: 30.0,
            color: activeIndex == 3? Colors.white : const Color(0xFFC8C9CB),
          ),
        ],
        onTap: (index) {
          setState(() {
            activeIndex = index;
          });
        },
      ),
      backgroundColor: Constants.primaryColor,
      body:
    IndexedStack(
      index: activeIndex,
      children: [
        HomeView(),
        const MapView(),
        const DistributorStoreScreen(),
        const EditProfileView(),

      ],
    )
    );
  }
}
