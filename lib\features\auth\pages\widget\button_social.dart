import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/app_font.dart';
import '../../../../core/widgets/custome_text.dart';


class ButtonSocial extends StatelessWidget {
  final Color colorButton;
  final Color colorText;
  final String textButton;
  final String iconButton;
  final GestureTapCallback? press;
  const ButtonSocial(
      {super.key,
      required this.colorButton,
      required this.colorText,
      required this.iconButton,
      this.press,
      required this.textButton});
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: press,
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(29),
          color: colorButton,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              iconButton,
              width: 22,
              height: 22,
            ),
            const SizedBox(
              width: 4,
            ),
            CustomizeText(
              title: textButton,
              fontSize: 12,
              maxLines: 1,
              fontFamily: Font.cairoSemiBold,
              color: colorText,
            ),
          ],
        ),
      ),
    );
  }
}
