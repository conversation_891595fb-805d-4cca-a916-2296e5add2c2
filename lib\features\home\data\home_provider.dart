import 'dart:convert';
import 'package:dio/dio.dart' as dio;
import 'package:flutter_temp/core/constant.dart';
import 'package:flutter_temp/features/auth/model/main.dart';
import 'package:flutter_temp/features/general/presentation/controllers/general_controller.dart';
import 'package:get/get.dart';

import '../../../core/api_provider.dart';
import 'models/customer_visits.dart';

class HomeProvider extends ApiProvider {
  Future<Response<DistributorVisits>> get_distributor_visits() =>
      sendGet<DistributorVisits>('rukn_distributor.api.get_distributor_visits',
          decoder: (obj) => DistributorVisits.fromJson(obj));

  Future<DistributorVisits?> getCustomerVisitts(String customer) async {
    final _dio = dio.Dio(); // Initialize Dio instance as a class field

    final data = {
      "customer": customer,
    };

    // Send to API
    final response = await _dio.get(
      '${baseApiUrl}rukn_distributor.api.get_distributor_visits',
      options: dio.Options(
        headers: {
          'Authorization': (Get.find<GeneralController>().token())!,
          'Content-Type': 'application/json'
        },
      ),
      data: data,
    );
    if (response.statusCode == 200) {
      return DistributorVisits.fromJson(response.data);
    } else {
      return null;
    }
  }

  // Update customer visit status
  Future<Response<Main>> updateCustomerVisitStatus(String customerVisitId, String status) =>
      sendPut<Main>(
        'rukn_distributor.api.update_customer_visit',
        {
          "customer_visit": customerVisitId,
          "status": status
        },
        decoder: (obj) => Main.fromJson(obj)
      );

  // Alternative method using Dio directly
  Future<Map<String, dynamic>?> updateCustomerVisitStatusDio(String customerVisitId, String status) async {
    try {
      final _dio = dio.Dio();

      // Print debug information
      print('Updating customer visit status:');
      print('Customer Visit ID: $customerVisitId');
      print('Status: $status');

      // Create FormData object exactly as shown in the example
      var formData = dio.FormData.fromMap({
        "customer_visit": customerVisitId,
        "status": status
      });

      // Print the request URL
      print('Request URL: ${baseApiUrl}rukn_distributor.api.update_customer_visit');

      // Make the API call
      final response = await _dio.put(
        '${baseApiUrl}rukn_distributor.api.update_customer_visit',
        data: formData,
        options: dio.Options(
          headers: {
            'Authorization': (Get.find<GeneralController>().token())!,
            // Don't set Content-Type when using FormData, Dio will set it automatically with boundary
          },
        ),
      );

      // Print the response for debugging
      print('Response status code: ${response.statusCode}');
      print('Response data: ${json.encode(response.data)}');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        print('Error response: ${response.statusMessage}');
        return null;
      }
    } catch (e) {
      print('Error updating customer visit status: $e');
      if (e is dio.DioException) {
        print('DioError type: ${e.type}');
        print('DioError message: ${e.message}');
        print('DioError response: ${e.response?.data}');
      }
      return null;
    }
  }
}
