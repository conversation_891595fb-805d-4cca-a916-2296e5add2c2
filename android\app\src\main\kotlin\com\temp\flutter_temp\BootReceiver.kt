package com.temp.flutter_temp

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.util.Log

class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
        private const val PREFS_NAME = "location_tracking_prefs"
        private const val KEY_TRACKING_ENABLED = "location_tracking_enabled"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Boot receiver triggered with action: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                // Check if location tracking was enabled before reboot
                val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val wasTrackingEnabled = prefs.getBoolean(KEY_TRACKING_ENABLED, false)
                
                if (wasTrackingEnabled) {
                    Log.d(TAG, "Location tracking was enabled, restarting service")
                    startLocationService(context)
                } else {
                    Log.d(TAG, "Location tracking was disabled, not starting service")
                }
            }
        }
    }
    
    private fun startLocationService(context: Context) {
        try {
            val serviceIntent = Intent(context, LocationTrackingService::class.java).apply {
                action = "START_LOCATION_TRACKING"
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
            Log.d(TAG, "Location service started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting location service", e)
        }
    }
}
