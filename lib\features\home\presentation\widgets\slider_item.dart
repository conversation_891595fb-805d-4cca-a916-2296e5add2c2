import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../../../../core/app_font.dart';
import '../../../../core/constant.dart';



class SilderItemWidget extends StatelessWidget {
  const SilderItemWidget({super.key,this.flag = true});
  final bool flag;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _onTap(context),
      child: SizedBox(
        width: double.infinity,
        // width: itemWidth,
//        height: itemWidth * 0.3,
        child: Stack(
          fit: StackFit.expand,
          children: [
            Padding(
                padding: const EdgeInsets.only(left: 0, right: 4),
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(flag ? 12 : 0),
                    child: CachedNetworkImage(
                      fit: BoxFit.fill,
                      imageUrl:
                      //placeholder url
                     placeholder,
                      placeholder: (context, url) =>
                          const Center(child: CircularProgressIndicator()),
                      errorWidget: (context, url, error) {
                        return Container(
                          height: 48,
                          width: 48,
                          decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              shape: BoxShape.circle),
                          child: const Icon(Icons.person),
                        );
                      },
                    )

                    //  Image.network(
                    //   item.image ?? placeholder,
                    //   fit: BoxFit.fill,
                    // ),

                    )),
            if (flag)
              const Positioned(
                  bottom: 20,
                  left: 30,
                  right: 20,
                  child: Text(
                     "XXXXXXXXXX",
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      color: Color.fromRGBO(255, 255, 255, 1),
                      fontFamily: Font.cairoRegular,
                      fontSize: 13,
                      fontWeight: FontWeight.normal,
                    ),
                  ))
          ],
        ),
      ),
    );
  }

  _onTap(context) {
    /// support to show the product detail
    // if (item.isProduct) {
    //   print("goooooo product");
    //   // Get.toNamed(Routes.ProductDetails + "?id=${item.id}");

    // }
    // if (item.isCategory) {
    //   //  Get.toNamed(Routes.Products +
    //   // '?category=--&title=${item.id}');
    // }

    // else if (widget.config["tab"] != null) {
    //   print("goooooo tab");

    //   return MainTabControlDelegate.getInstance()
    //       .changeTab(widget.config["tab"]);
    // } else if (widget.config["screen"] != null) {
    //   print("goooooo screen");

    //   return Navigator.of(context).pushNamed(widget.config["screen"]);
    // }

    /// support to show the post detail
    // else if (item.url != null) {
    //   print("goooooo url");

    //   Navigator.push(
    //     context,
    //     MaterialPageRoute(
    //       builder: (context) => Scaffold(
    //         appBar: AppBar(
    //           backgroundColor: Theme.of(context).primaryColorLight,
    //           leading: GestureDetector(
    //             child: Icon(Icons.arrow_back_ios),
    //             onTap: () => Navigator.pop(context),
    //           ),
    //         ),
    //         body: WebView(
    //           initialUrl: widget.config["url"],
    //           javascriptMode: JavascriptMode.unrestricted,
    //         ),
    //       ),
    //     ),
    //   );
    // }
  }
}
