import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/app_font.dart';


class ProductsGroup extends StatefulWidget {
  // final DataHome section;
  const ProductsGroup({
    super.key,
    // required this.section,
  });
  @override
  _ProductsGroupState createState() => _ProductsGroupState();
}

class _ProductsGroupState extends State<ProductsGroup> {
  var loading = true;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: true, // widget.section.services?.isNotEmpty ?? false,
      // tag: widget.section.name ?? '',
      child: SizedBox(
        // margin: EdgeInsets.only(bottom: 5),
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: <Widget>[
            const SizedBox(
              height: 10,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                   "TEST",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: Font.cairoRegular,
                    fontSize: 15,
                    color: Color(0xff000000),
                    letterSpacing: 0.35000000190734865,
                    fontWeight: FontWeight.w700,
                    height: 1.2727272727272727,
                  ),
                  textHeightBehavior:
                      TextHeightBehavior(applyHeightToFirstAscent: false),
                ),
                InkWell(
                  onTap: () {
                    // Get.toNamed(Routes.ProductsListView, arguments: [
                    //   {
                    //     "name": widget.section.name,
                    //     "id":
                    //         (widget.section.services?.first.category?.id ?? 0).toString()
                    //   }
                    // ]);


                  },
                  child: Row(
                    children: [
                      Text(
                        "ViewAll".tr,
                        textAlign: TextAlign.end,
                        style: const TextStyle(
                            color: Color.fromRGBO(107, 114, 128, 1),
                            fontFamily: Font.cairoSemiBold,
                            fontSize: 12,
                            letterSpacing: 0.30000001192092896,
                            fontWeight: FontWeight.normal,
                            height: 1.5),
                      ),
                      const Icon(
                        Icons.chevron_right_outlined,
                        color: Color.fromRGBO(107, 114, 128, 1),
                      )
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 15,
            ),
            Center(
              child: SizedBox(
                height: 230,
                width: MediaQuery.of(context).size.width,
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  itemCount: 5,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (BuildContext context, int index) {
                    return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 5.0),
                        width: MediaQuery.of(context).size.width / 2 - 50,
                        child:
                        const SizedBox()
                        //  ProductWidget()
                         );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
