// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:gap/gap.dart';
// import 'package:get/get.dart';

// class ErrorView extends StatelessWidget {
//   final Function? tryAgain;
//   const ErrorView({super.key, this.tryAgain});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: appBar(canBack: true, title: ""),
//       body: Container(
//           padding: EdgeInsets.all(20),
//           child: Center(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.center,
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Spacer(),
//                 SvgPicture.asset("assets/svg/error.svg"),
//                 SizedBox(
//                   height: 47,
//                 ),
//                 Text(
//                   "NoInternetConnection".tr,
               
//                 ),
//                 Gap(10),
//                 Text(
//                   "ItLooksLikeYourConnectionHasInteruptedPleaseCheckYourInternetProvider"
//                       .tr,
//                   style: localTextStyle(14, Color(0xff6B7280),
//                       fw: FontWeight.w700, height: 1.2),
//                   textAlign: TextAlign.center,
//                   textHeightBehavior:
//                       TextHeightBehavior(applyHeightToFirstAscent: false),
//                 ),
//                 SizedBox(
//                   height: 24,
//                 ),
//                 Spacer(),
//                 Row(
//                   children: [
//                     Gap(20),
//                     Expanded(
//                         child: mainButton(
//                             title: "TryAgain".tr,
//                             onTap: () {
//                               Get.back(result: true);
//                               tryAgain?.call();
//                             })),
//                     Gap(20),
//                   ],
//                 ),
//                   SizedBox(
//                   height: 24,
//                 ),
//               ],
//             ),
//           )),
//     );
//   }
// }
