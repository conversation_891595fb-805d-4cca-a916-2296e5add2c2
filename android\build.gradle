ext {
    kotlin_version     = '1.8.10'
    compileSdkVersion   = 34
    targetSdkVersion    = 34
    minSdkVersion       = 21
    appCompatVersion    = "1.6.1"
    playServicesLocationVersion = "21.3.0"
    hmsLocationVersion  = "6.12.0.300" //<-- this version invokes StrictMode crash "Explicit termination method 'end' not called" from its own okhttp requests!!
    removeBackgroundGeolocationDebugSoundsInRelease = false
}

allprojects {
    repositories {
        google()
        mavenCentral()
    
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
