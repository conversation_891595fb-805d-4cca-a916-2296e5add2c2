class Notifications {
  bool? status;
  List<NotificationsData>? data;
  int? total;
  int? perPage;
  int? currentPage;
  int? from;
  int? to;
  int? lastPage;
  double? executionTime;

  Notifications(
      {this.status,
      this.data,
      this.total,
      this.perPage,
      this.currentPage,
      this.from,
      this.to,
      this.lastPage,
      this.executionTime});

  Notifications.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <NotificationsData>[];
      json['data'].forEach((v) {
        data!.add(NotificationsData.fromJson(v));
      });
    }
    total = json['total'];
    perPage = json['per_page'];
    currentPage = json['current_page'];
    from = json['from'];
    to = json['to'];
    lastPage = json['last_page'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['total'] = total;
    data['per_page'] = perPage;
    data['current_page'] = currentPage;
    data['from'] = from;
    data['to'] = to;
    data['last_page'] = lastPage;
    data['execution_time'] = executionTime;
    return data;
  }
}

class NotificationsData {
  String? id;

  Item? item;
  String? lastActivity;
  String? readAt;
  List<Map>? items;

  bool isPost() => item?.parent_type == 'post';
  bool isTopic() => item?.parent_type == 'topic';
  bool isStream() => item?.parent_type == 'stream';
  bool isChat() => item?.parent_type == 'chat';
  bool isGift() => item?.parent_type == 'gift';
  bool isUser() => item?.parent_type == 'user';
  bool isChatStarted() => item?.parent_type == 'chat_started';

  @override
  String toString() => 'NotificationsData: $id, $item';
  NotificationsData({
    this.id,
    this.item,
    this.lastActivity,
    this.readAt,
    this.items,
  });

  NotificationsData.fromJson(Map<String, dynamic> json) {
    id = json['id'];

    item = json['item'] != null ? Item.fromJson(json['item']) : null;

    lastActivity = json['last_activity'];
    readAt = json['read_at'];
    if (json['items'] != null) {
      items = <Map>[];
      json['items'].forEach((v) {
        items!.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (item != null) {
      data['item'] = item!.toJson();
    }
    data['last_activity'] = lastActivity;
    data['read_at'] = readAt;
    if (items != null) {}
    return data;
  }
}

class Item {
  String? id;
  String? type;
  String? title;
  String? body;
  String? image_url;
  String? parent_id;
  String? parent_type; // post, topic, stream , chat
  Item(
      {this.id,
      this.type,
      this.title,
      this.body,
      this.image_url,
      this.parent_id,
      this.parent_type});

  Item.fromJson(Map<String, dynamic> json) {
    
    id = json['id'];
    type = json['type'];
    title = json['title'];
    body = json['body'];
    image_url = json['image_url'];
    parent_id = json['parent_id'];
    parent_type = json['parent_type'];
  }

  @override
  String toString() => 'Item: $id, $title, $body, $parent_id $parent_type';
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['type'] = type;
    data['title'] = title;
    data['body'] = body;
    return data;
  }
}
