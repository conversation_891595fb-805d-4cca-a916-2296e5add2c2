import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/app_font.dart';
import '../../../core/widgets/custome_text.dart';


class LanguagePanelView extends StatelessWidget {
  const LanguagePanelView({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        GestureDetector(
          // onTap: () => Get.to(() => LangView()),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8,vertical: 5),
            margin: const EdgeInsets.only(top: 60),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.all(
                Radius.circular(30),
              ),
              border: Border.all(color: Colors.grey, width: 2),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: SvgPicture.asset(
                    "assets/images/settings/lang.svg",
                  ),
                ),
                const SizedBox(width: 4),
                const CustomizeText(
                  title: "language",
                  textAlign: TextAlign.start,
                  fontFamily: Font.cairoBold,
                  fontSize: 12,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
