import 'package:flutter/material.dart';
import 'package:flutter_temp/core/color_app.dart';

class SegmentedButtonCategory extends StatefulWidget {
  final List<String> segments;
  final Function(int) onValueChanged;

  const SegmentedButtonCategory(
      {super.key, required this.segments, required this.onValueChanged});

  @override
  _SegmentedButtonState createState() => _SegmentedButtonState();
}

class _SegmentedButtonState extends State<SegmentedButtonCategory> {
  int selectedSegment = 0;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: List.generate(
          widget.segments.length,
          (index) => GestureDetector(
            onTap: () {
              setState(() {
                selectedSegment = index;
                widget.onValueChanged(index);
              });
            },
            child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
              margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              decoration: BoxDecoration(
                color: index == selectedSegment
                    ? ColorApp.colorPrimary
                    : Colors.transparent, // Adjust colors as needed
                borderRadius: BorderRadius.circular(8.0), // Adjust the radius
                border: Border.all(
                  color: index == selectedSegment
                      ? Colors.transparent
                      : ColorApp.colorGray,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.circle,
                    // size: 8,
                    color: index == selectedSegment
                        ? Colors.white
                        : ColorApp.colorGray,
                  ),
                  Text(
                    widget.segments[index],
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Givonic',
                      // fontWeight: FontWeight.w600,
                      color: index == selectedSegment
                          ? Colors.white
                          : ColorApp.colorGray,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
