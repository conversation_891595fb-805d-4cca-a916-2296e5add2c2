import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter_temp/core/color_app.dart';
import 'package:get/get.dart';

import '../features/general/provider/general_provider.dart';

var notificationServiceStream = StreamController<RemoteMessage>.broadcast();
late FirebaseRemoteConfig remoteConfig;

class NotificationService {
  static Future<void> init() async {
    print("NotificationService init");
    FirebaseMessaging.instance.setAutoInitEnabled(true);

    NotificationSettings settings =
        await FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    print("NotificationService init");

    print('User granted permission: ${settings.authorizationStatus}');

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Got a message whilst in the foreground!');
      print('Message data: ${message.data}');
      notificationServiceStream.add(message);
      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
      }

      Get.showSnackbar(GetSnackBar(
        title: "TikOne".tr,
        message: message.notification?.title ?? "-",
        duration: const Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
        onTap: (sn) {
          handelPages(message);
        },
        backgroundColor: ColorApp.colorPrimary,
      ));
    });

    remoteConfig = FirebaseRemoteConfig.instance;
    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: const Duration(hours: 1),
    ));

    await remoteConfig.setDefaults(const {
      "social_login": "1",
      "base_url": "https://api.tikone.net/",
      "pusherToken": "11963ae76252b34e0009",
      "agoraAppId": "5a363832ded74366bcf981eb9e46203c",
      "cloudflare_accountId": "cd210f08c2868ff3e1be3005734b689b",
      "cloudflare_token": "****************************************"
    });

    try {
      await remoteConfig.fetchAndActivate();
    } catch (e) {}
    // FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    //     alert: true, badge: false, sound: true);
  }

  static handelOffline() async {
    var message = await FirebaseMessaging.instance.getInitialMessage();
    if (message != null) {
      handelPages(message);
    }

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('A new onMessageOpenedApp event was published!');
      print('Got a message whilst in the foreground!');
      print('Message data: ${message.data}');
      handelPages(message);
      notificationServiceStream.add(message);
      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
      }
    });
  }

  static handelPages(RemoteMessage ntf) async {
    // Uri uri = Uri.parse(url);
// {comment_id: 0, parent_type: topic, id: 16a0b464-6204-4b4c-b52d-59b737532fcd, parent_id: 66e90749-8e1b-4c99-9b06-5d4a0d22beb2, type: comment_received, comment_reply_id: 0, click_action: FLUTTER_NOTIFICATION_CLICK}
//     String? id = ntf.data["parent_id"] ?? "-";
    // String? type = ntf.data["parent_type"] ?? "-";
    // if (type == "stream") {
    //   var loading = DialogLoading.of();
    //   loading.show();
    //   var provider = StreamProvider();
    //   var details = await provider.streamDetails(streamID: id);
    //   loading.hide();
    //   if (details.isOk && details.body?.data?.finishedAt == null) {
    //     Get.to(StartLiveStream(
    //       isBroadcaster: false,
    //       streamID: id,
    //     ));
    //   } else {
    //     showError("يبدو ان البث قد انتهي ");
    //   }
    // } else if (type == "post") {
    //   var loading = DialogLoading.of();
    //   loading.show();
    //   var provider = PostProvider();
    //   var detils = await provider.postDetails(id: id);
    //   loading.hide();
    //
    //   if (detils.isOk && detils.body?.data != null) {
    //     Get.to(VideoProfilePage(postList: [detils.body!.data!], index: 0));
    //   }
    // } else if (type == "topic") {
    //   print("topic");
    //   var loading = DialogLoading.of();
    //   loading.show();
    //   var provider = TopicProvider();
    //   var detils = await provider.topicsDetails(id: id);
    //   loading.hide();
    //
    //   if (detils.isOk && detils.body?.data != null) {
    //     Get.to(PostDetailsPage(topic: detils.body!.data!));
    //   }
    // }
    //
    // else if (type == "gift") {
    //   print("topic");
    //
    //     Get.to(GiftsView());
    //
    // }
  }

  static updateToken() {
    print("Notification Token start");

    FirebaseMessaging.instance.getToken().then((value) {
      print("Notification Token $value");
      //check if GeneralProvider registered
      if (!Get.isRegistered<GeneralProvider>()) {
        Get.put(GeneralProvider(), permanent: true);
      }
      Get.find<GeneralProvider>().updateFcm(value);
    }).catchError((error) {
      print("notification error: $error");
    });
  }
}
