import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constant.dart';
import '../../../features/orders/data/models/DistributorStore.dart';
import '../data/store_provider.dart';

class StoreController extends GetxController {
  final StoreProvider storeProvider;
  StoreController({required this.storeProvider});

  DistributorStore? distributorStore;
  List<String> categories = ['All'];
  String selectedCategory = 'All';
  bool isLoading = true;

  @override
  void onInit() {
    super.onInit();
  }

  void setSelectedCategory(String category) {
    selectedCategory = category;
    update();
  }

  Future<void> loadStoreData() async {
    isLoading = true;
    update();

    try {
      await storeProvider.distributor_store().then((result) {
        if (result.message?.status?.success == true) {
          distributorStore = result;
          
          // Extract unique categories from items
          final uniqueCategories = <String>{};
          distributorStore?.message?.data?.itemsStock?.forEach((item) {
            if (item.itemGroup != null && item.itemGroup!.isNotEmpty) {
              uniqueCategories.add(item.itemGroup!);
            }
          });
          
          // Update categories list
          categories = ['All', ...uniqueCategories.toList()];
          
          update();
        } else {
          showError(result.message?.status?.message ?? "Failed to load store data");
        }
      });
    } catch (e) {
      showError("Error loading store data: $e");
    } finally {
      isLoading = false;
      update();
    }
  }

  List<ItemsStock> get filteredItems {
    if (distributorStore?.message?.data?.itemsStock == null) {
      return [];
    }
    
    if (selectedCategory == 'All') {
      return distributorStore!.message!.data!.itemsStock!;
    } else {
      return distributorStore!.message!.data!.itemsStock!
          .where((item) => item.itemGroup == selectedCategory)
          .toList();
    }
  }
}
