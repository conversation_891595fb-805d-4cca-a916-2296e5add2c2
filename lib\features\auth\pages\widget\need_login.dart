import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

import '../../../../core/widgets/button_app.dart';
import '../../../../routes/app_routes.dart';


class NeedLogin extends StatelessWidget {
  const NeedLogin({super.key});



  @override
  Widget build(BuildContext context) {
    return Container(
        child: Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset("assets/svg/need_login.svg"),
          const SizedBox(
            height: 47,
          ),
          Text(
            "YouAreNotRegistered".tr,

          ),
          const Gap(10),
          Text(
            "LogInToAccessYourAccount".tr,

            textHeightBehavior:
                const TextHeightBehavior(applyHeightToFirstAscent: false),
          ),
          const SizedBox(
            height: 24,
          ),
          Row(
            children: [
              const Gap(20),
              Expanded(
                  child: ButtonApp(
                      title: "Login".tr,
                      press: () {
                        Get.toNamed(Routes.login);
                      })),
              const Gap(10),
              Expanded(
                child: ButtonApp(
                    title: "SignUp".tr,
                    press: () {
                      Get.toNamed(Routes.signUp);
                    },
                 ),
              ),
              const Gap(20),
            ],
          )
        ],
      ),
    ));
  }
}
