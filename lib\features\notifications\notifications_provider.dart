// import 'package:get/get.dart';
// import 'package:flutter_temp/features/auth/model/main.dart';
// import 'package:flutter_temp/features/inbox/data/chat_details.dart';
// import 'package:flutter_temp/features/inbox/data/chats.dart';
// import 'package:flutter_temp/features/notifications/data/notifications.dart';
//
// import '../../core/api_provider.dart';
//
//
// class NotificationsProvider extends ApiProvider {
//   Future<Response<Chats>> getChats() => loading(false)
//       .sendGet<Chats>('chats', decoder: (obj) => Chats.fromJson(obj));
//   Future<Response<Notifications>> getNotifications() => loading(false)
//       .sendGet<Notifications>('user/notifications', decoder: (obj) => Notifications.fromJson(obj));
//
//   Future<Response<ChatDetails>> getChatsDetails(chatID) =>
//       loading(true).sendGet<ChatDetails>('chats/$chatID',
//           decoder: (obj) => ChatDetails.fromJson(obj));
//
//    Future<Response<Main>>  sendMessage(String chatID, String message) {
//     return loading(true).sendPost('chats/$chatID/send_message',{
//       "message": message
//     } ,decoder: (obj) => Main.fromJson(obj));
//   }
//    Future<Response<Main>>  startChat(String userID) {
//     return loading(true).sendPost('profiles/$userID/start_chat' ,{},decoder: (obj) => Main.fromJson(obj));
//   }
// }
