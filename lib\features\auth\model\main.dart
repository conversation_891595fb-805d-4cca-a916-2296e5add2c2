class Main {
  Message? message;

  Main({this.message});

  Main.fromJson(Map<String, dynamic> json) {
    message =
        json['message'] != null ? new Message.fromJson(json['message']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.message != null) {
      data['message'] = this.message!.toJson();
    }
    return data;
  }
}

class Message {
  Status? status;
  List<Errors>? errors;

  Message({this.status, this.errors});

  //get error list as String
  String getErrorList() {
    String errorList = "";
    errors!.forEach((element) {
      errorList += (element.error ?? "") + "\n";
    });
    return errorList;
  }

  Message.fromJson(Map<String, dynamic> json) {
    status =
        json['status'] != null ? new Status.fromJson(json['status']) : null;
    if (json['errors'] != null) {
      errors = <Errors>[];
      json['errors'].forEach((v) {
        errors!.add(new Errors.fromJ<PERSON>(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.status != null) {
      data['status'] = this.status!.toJson();
    }
    return data;
  }
}

class Status {
  String? message;
  bool? success;
  int? code;

  Status({this.message, this.success, this.code});

  Status.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    success = json['success'];
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['success'] = this.success;
    data['code'] = this.code;
    return data;
  }
}

class Errors {
  String? key;
  String? error;

  Errors({this.key, this.error});

  Errors.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    error = json['error'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['key'] = this.key;
    data['error'] = this.error;
    return data;
  }
}
