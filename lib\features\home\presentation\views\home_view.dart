import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constants.dart';
import 'package:flutter_temp/core/widgets/latest_orders.dart';
import 'package:get/get.dart';
import 'package:msh_checkbox/msh_checkbox.dart';

import '../../../general/presentation/controllers/general_controller.dart';
import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  HomeView({super.key});
  final CarouselController _controller = CarouselController();
  final controllerP = PageController(initialPage: 999);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
        // init: controller,
        initState: (_) {
      Future.delayed(Duration.zero, () {
        controller.loadDistributorVisits();
      });
    }, builder: (controller) {
      return Stack(
        clipBehavior: Clip.none,
        children: [
          // Positioned(
          //   right: 0.0,
          //   top: -20.0,
          //   child: Opacity(
          //     opacity: 0.3,
          //     child: Image.asset(
          //       "assets/images/washing_machine_illustration.png",
          //     ),
          //   ),
          // ),
          SingleChildScrollView(
            child: Container(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: kToolbarHeight,
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // GestureDetector(
                        //   onTap: () {
                        //     Navigator.pop(context);
                        //   },
                        //   child: Icon(
                        //     FlutterIcons.keyboard_backspace_mdi,
                        //     color: Colors.white,
                        //   ),
                        // ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: "Welcome Back,\n",
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge!
                                        .copyWith(
                                          color: Colors.white,
                                        ),
                                  ),
                                  TextSpan(
                                    text:
                                        "${Get.find<GeneralController>().currentUser?.employeeId}",
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleLarge!
                                        .copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  )
                                ],
                              ),
                            ),
                            Image.asset(
                              "assets/images/dp.png",
                            )
                          ],
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8.0, horizontal: 5),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          child: Row(
                            children: [
                              MSHCheckbox(
                                size: 60,
                                value: controller.isChecked,
                                colorConfig:
                                    MSHColorConfig.fromCheckedUncheckedDisabled(
                                  checkedColor: Constants.primaryColor,
                                ),
                                style: MSHCheckboxStyle.stroke,
                                onChanged: (selected) {
                                  controller.toggleTracking(selected);
                                },
                              ),
                              const SizedBox(width: 10.0),
                              Text(
                                "Tracking ${controller.isChecked ? "Enabled" : "Disabled"}",
                                style: Theme.of(context)
                                    .textTheme
                                    .titleLarge!
                                    .copyWith(
                                      color: Colors.black,
                                    ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 50.0,
                  ),
                  Container(
                    width: double.infinity,
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height - 200.0,
                    ),
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30.0),
                        topRight: Radius.circular(30.0),
                      ),
                      color: Constants.scaffoldBackgroundColor,
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: 24.0,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Padding(
                        //   padding: EdgeInsets.symmetric(
                        //     horizontal: 24.0,
                        //   ),
                        //   child: Text(
                        //     "New Locations",
                        //     style: TextStyle(
                        //       color: Color.fromRGBO(19, 22, 33, 1),
                        //       fontSize: 18.0,
                        //     ),
                        //   ),
                        // ),
                        SizedBox(height: 7.0),
                        // Container(
                        //   height: ScreenUtil().setHeight(100.0),
                        //   child: Center(
                        //     // lets make a widget for the cards
                        //     child: LocationSlider(),
                        //   ),
                        // ),
                        LatestOrders(
                            loading: controller.loadingDistributorVisits.value,
                            customerVisits: controller.distributorVisits
                                ?.message?.data?.customerVisits),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      );
    });
  }
}
