import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_temp/core/app_font.dart';
import 'package:flutter_temp/core/color_app.dart';
import 'package:flutter_temp/core/widgets/custome_text.dart';
import 'package:get/get.dart';

class AppBarCustom extends StatelessWidget {
  final String title;
  const AppBarCustom({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 9),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => Get.back(),
            child: const Icon(
              Icons.arrow_back_rounded,
              color: Colors.black,
            ),
          ),
          Row(
            children: [
              SvgPicture.asset(
                "assets/images/logo_app_bar.svg",
              ),
              const SizedBox(
                width: 9,
              ),
              CustomizeText(
                title: title,
                textAlign: TextAlign.center,
                fontFamily: Font.cairoSemiBold,
                fontSize: 14,
                color: ColorApp.colorText,
              )
            ],
          ),
          const SizedBox(
            width: 5,
          ),
        ],
      ),
    );
  }
}

buildProfile(String url, {double size = 50}) {
  return SizedBox(
    width: size,
    height: size,
    child: ClipRRect(
      borderRadius: BorderRadius.circular(size),
      child: Image(
        image: NetworkImage(
          url,
        ),
        fit: BoxFit.cover,
      ),
    ),
  );
}
