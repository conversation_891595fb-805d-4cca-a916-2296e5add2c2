// // ignore_for_file: unused_field

// import 'package:flutter_temp/features/auth/model/social_account_user.dart';
// import 'package:google_sign_in/google_sign_in.dart';

// class GoogleAccountLoginService {
//   GoogleSignInAccount? _currentUser;
//   final GoogleSignIn _googleSignIn = GoogleSignIn(
//     scopes: ['email'],
//   );
//   late GoogleSignInAuthentication _googleSignInAuthentication;
//   static GoogleAccountLoginService? _instance;

//   static GoogleAccountLoginService get instance {
//     _instance ??= GoogleAccountLoginService();

//     return _instance!;
//   }

//   Future<SocialAccountUser> login() async {
//     _currentUser = await _googleSignIn.signIn();
//     _googleSignInAuthentication = await _currentUser!.authentication;
//     final socialAccountUser = SocialAccountUser(
//         email: _currentUser!.email,
//         provider: 'google',
//         providerId: _currentUser!.id,
//         token: _googleSignInAuthentication.accessToken);
//     print("GoogleAuth  ${socialAccountUser.toString()}");
//     await _googleSignIn.signOut();
//     return socialAccountUser;
//   }
// }
