// import 'dart:ui';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/widgets.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:get/get.dart';
// import 'package:gap/gap.dart';
// import 'package:flutter_temp/core/color_app.dart';
// import 'package:flutter_temp/core/constant.dart';
//
// import 'package:flutter_temp/core/universal_variables.dart';
// import 'package:flutter_temp/core/widgets/dialog_loading.dart';
// import 'package:flutter_temp/features/inbox/data/chats.dart';
// import 'package:flutter_temp/features/inbox/view/inbox_screen.dart';
// import 'package:flutter_temp/features/notifications/data/notifications.dart';
// import 'package:flutter_temp/features/notifications/view/live_widget.dart';
// import 'package:flutter_temp/features/notifications/view/new_live_widget.dart';
// import 'package:flutter_temp/features/page/view/shop/view/gifts_view.dart';
// import 'package:flutter_temp/features/posts/post_provider.dart';
// import 'package:flutter_temp/features/posts/view/video_profile_page.dart';
// import 'package:flutter_temp/features/topic/topic_provider.dart';
// import 'package:flutter_temp/features/topic/view/post_details_view.dart';
// import 'package:flutter_temp/main.dart';
// import 'package:flutter_temp/routes/app_routes.dart';
// import 'package:timeago/timeago.dart' as time_ago;
//
// import '../../../core/widgets/custome_text.dart';
// import '../../inbox/view/chat_page.dart';
// import '../../streams/stream_provider.dart';
// import '../../streams/view/start_live.dart';
// import '../controller/notifications_controller.dart';
//
// class NotificationsScreen extends GetView<NotificationsController> {
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<NotificationsController>(
//         init: Get.find<NotificationsController>()..loadData(),
//         initState: (_) {},
//         builder: (controller) => Scaffold(
//               backgroundColor: Colors.white,
//               appBar: AppBar(
//                 elevation: 0,
//                 backgroundColor: Colors.white,
//                 title: Text(
//                   "Notifications".tr,
//                   style: openSans(18, Color(0xff1F2124), FontWeight.w600),
//                 ),
//                 actions: [
//                   // Container(
//                   //   margin: EdgeInsetsDirectional.only(end: 8.w),
//                   //   child: SvgPicture.asset("assets/images/add_user.svg"),
//                   // ),
//                 ],
//                 centerTitle: true,
//               ),
//               body: Container(
//                 padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 0.h),
//                 child: SingleChildScrollView(
//                   child: Column(
//                     children: [
//                       SingleChildScrollView(
//                         scrollDirection: Axis.horizontal,
//                         child: Row(
//                           children: [
//                             Gap(5),
//                             NewLive(),
//                             for (var i in controller.streams) LiveWidget(i),
//                           ],
//                         ),
//                       ),
//                       Row(
//                         children: [
//                           CustomizeText(
//                               title: "Activty Feed".tr,
//                               fontSize: 14,
//                               fontWeight: FontWeight.w600),
//                           Spacer(),
//                           Icon(
//                             Icons.arrow_forward_ios,
//                             size: 14,
//                           )
//                         ],
//                       ),
//                       controller.isLoadingNotifications
//                           ? Center(
//                               child: CircularProgressIndicator(),
//                             )
//                           : ListView.builder(
//                               shrinkWrap: true,
//                               physics: NeverScrollableScrollPhysics(),
//                               itemCount: controller.notifications.length,
//                               itemBuilder: (BuildContext context, int index) {
//                                 return activtyCard(
//                                     controller.notifications[index]);
//                               }),
//                       Gap(10),
//                       InkWell(
//                         onTap: () {
//                           Get.toNamed(Routes.Inbox);
//                         },
//                         child: Row(
//                           children: [
//                             CustomizeText(
//                                 title: "Conversations".tr,
//                                 fontSize: 14,
//                                 fontWeight: FontWeight.w600),
//                             Spacer(),
//                             Icon(
//                               Icons.arrow_forward_ios,
//                               size: 14,
//                             )
//                           ],
//                         ),
//                       ),
//                       controller.isLoadingConversations
//                           ? Center(
//                               child: CircularProgressIndicator(),
//                             )
//                           : ListView.builder(
//                               shrinkWrap: true,
//                               physics: NeverScrollableScrollPhysics(),
//                               itemCount: controller.chats?.data?.length,
//                               itemBuilder: (BuildContext context, int index) {
//                                 var item = controller.chats!.data![index];
//                                 return messegesCard(item,controller);
//                               }),
//                     ],
//                   ),
//                 ),
//               ),
//             ));
//   }
//
//   Widget activtyCard(NotificationsData notification) {
//     return GestureDetector(
//       behavior: HitTestBehavior.translucent,
//       onTap: () async {
//         print(notification);
//         if (notification.isStream()) {
//           var loading = DialogLoading.of();
//           loading.show();
//           var provider = StreamProvider();
//           var details = await provider.streamDetails(
//               streamID: notification.item?.parent_id!);
//           loading.hide();
//           if (details.isOk && details.body?.data?.finishedAt == null) {
//             Get.to(StartLiveStream(
//               isBroadcaster: false,
//               streamID: notification.item?.parent_id,
//             ));
//           } else {
//             showError("يبدو ان البث قد انتهي ");
//           }
//         } else if (notification.isPost()) {
//           var loading = DialogLoading.of();
//           loading.show();
//           var provider = PostProvider();
//           var detils =
//               await provider.postDetails(id: notification.item?.parent_id!);
//           loading.hide();
//
//           if (detils.isOk && detils.body?.data != null) {
//             Get.to(VideoProfilePage(postList: [detils.body!.data!], index: 0));
//           }
//         } else if (notification.isTopic()) {
//           var loading = DialogLoading.of();
//           loading.show();
//           var provider = TopicProvider();
//           var detils =
//               await provider.topicsDetails(id: notification.item?.parent_id!);
//           loading.hide();
//
//           if (detils.isOk && detils.body?.data != null) {
//             Get.to(PostDetailsPage(topic: detils.body!.data!));
//           }
//         } else if (notification.isGift()) {
//           Get.to(GiftsView());
//         } else if (notification.isUser() &&
//             notification.item?.parent_id != null) {
//           Get.toNamed(Routes.ProfileScreen, parameters: {
//             "isProfileUser": "true",
//             "userId": notification.item!.parent_id!
//           });
//         } else if (notification.isChatStarted() &&
//             notification.item?.parent_id != null) {
//           Get.toNamed(Routes.Inbox);
//           // Get.toNamed(Routes.ProfileScreen, parameters: {
//           //   "isProfileUser": "true",
//           //   "userId": notification.item!.parent_id!
//           // });
//         }
//       },
//       child: Container(
//         width: Size.infinite.width,
//         // margin: EdgeInsetsDirectional.only(bottom: 18.w),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.start,
//           children: [
//             Padding(
//               padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 5.h),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Container(
//                     // margin: EdgeInsetsDirectional.only(end: 14.w),
//                     child: CircleAvatar(
//                       radius: 24.0,
//                       backgroundImage: NetworkImage(
//                           notification.item?.image_url ?? placeholder),
//                       backgroundColor: Colors.white,
//                     ),
//                   ),
//                   Gap(4),
//                   Expanded(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         CustomizeText(
//                             title: notification.item?.title ?? "-",
//                             fontSize: 12,
//                             fontWeight: FontWeight.normal),
//                         Row(
//                           children: [
//                             Expanded(
//                               child: CustomizeText(
//                                   title: notification.item?.body ?? "",
//                                   fontSize: 12,
//                                   fontWeight: FontWeight.normal),
//                             ),
//                             CustomizeText(
//                                 title:
//                                     " ${time_ago.format(DateTime.parse(notification.lastActivity!), locale: box.read("lang") ?? "ar")}",
//                                 fontSize: 12,
//                                 color: ColorApp.colorGray,
//                                 fontWeight: FontWeight.normal),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget messegesCard(ChatData chat, NotificationsController controller) {
//     return InkWell(
//       onTap: () async {
//         await Get.to(ChatPage(chat));
//
//         controller.loadData();
//       },
//       child: Container(
//         width: Size.infinite.width,
//         // margin: EdgeInsetsDirectional.only(bottom: 18.w),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.start,
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Padding(
//               padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 5.h),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Container(
//                     // margin: EdgeInsetsDirectional.only(end: 14.w),
//                     child: CircleAvatar(
//                       radius: 24.0,
//                       backgroundImage: NetworkImage(chat.users
//                               ?.firstWhereOrNull((element) => true)
//                               ?.avatarUrl ??
//                           placeholder),
//                       backgroundColor: Colors.white,
//                     ),
//                   ),
//                   Gap(4),
//                   Expanded(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         CustomizeText(
//                             title: chat.users
//                                     ?.firstWhereOrNull((element) => true)
//                                     ?.name ??
//                                 chat.users
//                                     ?.firstWhereOrNull((element) => true)
//                                     ?.username ??
//                                 "-",
//                             fontSize: 12,
//                             fontWeight: FontWeight.normal),
//                         CustomizeText(
//                             title: chat.lastMessage?.message?.body ??
//                                 "No message yet",
//                             fontSize: 12,
//                             fontWeight: FontWeight.normal),
//                       ],
//                     ),
//                   ),
//                   Gap(4),
//                   Column(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       CustomizeText(
//                           title: time_ago.format(DateTime.parse(
//                               chat.lastMessage?.sentAt ??
//                                   "2021-09-29T12:00:00.000Z")),
//                           fontSize: 12,
//                           fontWeight: FontWeight.normal),
//                       if ((chat.unseenCount ?? 0) > 0)
//                         Container(
//                           width: 25,
//                           height: 25,
//                           alignment: Alignment.center,
//                           // padding: EdgeInsets.all(1),
//                           decoration: BoxDecoration(
//                               color: ColorApp.colorBadge,
//                               borderRadius: BorderRadius.circular(25)),
//                           // margin: EdgeInsetsDirectional.only(top: 4.h),
//                           child: CustomizeText(
//                             title: "${chat.unseenCount}",
//                             fontSize: 12,
//                             textAlign: TextAlign.center,
//                             fontWeight: FontWeight.w500,
//                             color: Colors.white,
//                           ),
//                         )
//                     ],
//                   )
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
