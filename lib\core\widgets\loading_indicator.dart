import 'package:flutter/material.dart';
import 'package:loading_indicator/loading_indicator.dart' as ind;

class LoadingIndicator extends StatelessWidget {
  final ind.Indicator? indicatorType;
   const LoadingIndicator({super.key,this.indicatorType });

  @override
  Widget build(BuildContext context) =>  Center(
        child: SizedBox(
          width: 50,
          height: 50,
          child: ind.LoadingIndicator(
            indicatorType:indicatorType?? ind.Indicator.ballClipRotatePulse,
          ),
        ),
      );
}
