// // ظظimport 'dart:async';
//
// import 'package:get/get.dart';
//
// import '../inbox/inbox_provider.dart';
// import '../streams/stream_provider.dart';
// import 'controller/notifications_controller.dart';
// import 'notifications_provider.dart';
//
// class NotificationsBinding extends Bindings {
//   @override
//   void dependencies() {
//        Get.lazyPut(() => InboxProvider(), fenix: true);
//     Get.lazyPut(() => StreamProvider(), fenix: true);
//     Get.lazyPut(() => NotificationsProvider(), fenix: true);
//
//
//     Get.lazyPut<NotificationsController>(
//         () => NotificationsController(Get.find(), Get.find(), Get.find()),
//         fenix: true);
//   }
// }
