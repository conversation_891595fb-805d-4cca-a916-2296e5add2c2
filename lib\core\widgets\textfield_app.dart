import 'package:flutter/material.dart';
import 'package:flutter_temp/core/app_font.dart';
import 'package:get/get.dart';

class TextFieldApp extends StatefulWidget {
  final TextEditingController? controller;
  final String? lable;
  final String? hint;
  final int numberLine;
  final bool isPassword;
  final bool isBorder;
  final bool enable;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final TextInputType? keyboardType;
  const TextFieldApp(
      {super.key,
      this.controller,
      this.lable,
      this.keyboardType,
      this.onChanged,
      this.validator,
      this.numberLine = 1,
      this.isPassword = false,
      this.enable = true,
      this.isBorder = false,
      this.hint});

  @override
  _TextFieldAppState createState() => _TextFieldAppState();
}

class _TextFieldAppState extends State<TextFieldApp> {
  var _passwordVisible = true;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      enabled: widget.enable,
      validator: widget.validator,
      onChanged: widget.onChanged,
      style: const TextStyle(
          fontFamily: Font.cairoSemiBold, fontSize: 13, ),
      obscureText: widget.isPassword ? _passwordVisible : false,
      controller: widget.controller,
      keyboardType: widget.keyboardType,
      maxLines: widget.numberLine,
      decoration: InputDecoration(
          filled: true,
          fillColor: widget.isBorder ? Colors.white : const Color(0xffF5F5F5),
          suffixIcon: widget.isPassword
              ? IconButton(
                  icon: Icon(
                    // Based on passwordVisible state choose the icon
                    _passwordVisible
                        ? Icons.visibility_outlined
                        : Icons.visibility_off_outlined,
                    color: const Color(0xffB6B6BD),
                  ),
                  onPressed: () {
                    // Update the state i.e. toogle the state of passwordVisible variable
                    setState(() {
                      _passwordVisible = !_passwordVisible;
                    });
                  },
                )
              : null,
          // labelText: "${widget.lable}".tr,
          hintText: "${widget.hint}".tr,
          hintStyle: const TextStyle(
              fontFamily: Font.cairoSemiBold,
              fontSize: 13,
              ),
          // prefixIcon: Icon(Icons.email),
          // labelStyle: TextStyle(
          //     fontFamily: Font.cairoSemiBold,
          //     fontSize: 12,
          //     color: textPlaceHolderColor),
          // backgroundColor:Color(0xffF5F5F5),
          focusedBorder: borderTextFiled(),
          disabledBorder: borderTextFiled(),
          enabledBorder: borderTextFiled(),
          border: borderTextFiled()),
    );
  }

  InputBorder borderTextFiled() {
    return OutlineInputBorder(
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        borderSide: BorderSide(
            width: 1,
            color: widget.isBorder == false
                ? const Color(0xffF5F5F5)
                : const Color(0xffD1D1D1)));
  }
}

InputBorder borderTextFiled() {
  return const OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(8)),
      borderSide: BorderSide(width: 1, color: Color(0xffD1D1D1)));
}
