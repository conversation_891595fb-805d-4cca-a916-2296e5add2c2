class SocialAccountUser {
  final String? email;
  final String? name;
  final String? provider;
  final String? providerId;
  final String? token;

  SocialAccountUser(
      {this.email, this.name, this.provider, this.providerId, this.token});

  @override
  String toString() {
    return 'SocialAccountUser{email: $email, name: $name, provider: $provider, providerId: $providerId, token: $token}';
  }
}
