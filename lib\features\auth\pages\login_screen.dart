import 'package:flutter/material.dart';
import 'package:flutter_icons_null_safety/flutter_icons_null_safety.dart';
import 'package:flutter_temp/features/auth/controller/auth_controller.dart';
import 'package:get/get.dart';

import '../../../core/constants.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/widgets/input_widget.dart';

class LoginScreen extends GetView<AuthController> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final GlobalKey<FormState> _fromKey = GlobalKey<FormState>();

  LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuthController>(
        // init: MyController(),
        initState: (_) {},
        builder: (_) => Scaffold(
              backgroundColor: Constants.primaryColor,
              body: SafeArea(
                bottom: false,
                child: Container(
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      SingleChildScrollView(
                        child: Container(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16.0,
                                  vertical: 15.0,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: const Icon(
                                        FlutterIcons.keyboard_backspace_mdi,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20.0,
                                    ),
                                    Text(
                                      "Log in to your account",
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge!
                                          .copyWith(
                                            fontWeight: FontWeight.w600,
                                            color: Colors.white,
                                          ),
                                    )
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: 40.0,
                              ),
                              Flexible(
                                child: Container(
                                  width: double.infinity,
                                  constraints: BoxConstraints(
                                    minHeight:
                                        MediaQuery.of(context).size.height -
                                            180.0,
                                  ),
                                  decoration: const BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(30.0),
                                      topRight: Radius.circular(30.0),
                                    ),
                                    color: Colors.white,
                                  ),
                                  padding: const EdgeInsets.all(24.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.stretch,
                                    children: [
                                      // Lets make a generic input widget
                                      InputWidget(
                                          topLabel: "Email",
                                          hintText: "Enter your email address",
                                          controller: _emailController),
                                      const SizedBox(
                                        height: 25.0,
                                      ),
                                      InputWidget(
                                        topLabel: "Password",
                                        obscureText: true,
                                        controller: _passwordController,
                                        hintText: "Enter your password",
                                      ),
                                      const SizedBox(
                                        height: 15.0,
                                      ),
                                      GestureDetector(
                                        onTap: () {},
                                        child: const Text(
                                          "Forgot Password?",
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            color: Constants.primaryColor,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 20.0,
                                      ),

                                      AppButton(
                                        type: ButtonType.PRIMARY,
                                        text: "Log In",
                                        onPressed: () => controller.login(
                                            username: _emailController.text,
                                            password: _passwordController.text),
                                      )
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
  }
}
