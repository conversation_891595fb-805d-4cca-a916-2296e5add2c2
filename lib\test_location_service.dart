// lib/test_location_service.dart
// ملف اختبار بسيط لخدمة الموقع

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'core/services/app_lifecycle_service.dart';
import 'core/services/location_service.dart';

class LocationServiceTestPage extends StatefulWidget {
  const LocationServiceTestPage({Key? key}) : super(key: key);

  @override
  State<LocationServiceTestPage> createState() => _LocationServiceTestPageState();
}

class _LocationServiceTestPageState extends State<LocationServiceTestPage> {
  final LocationService _locationService = LocationService();
  late final AppLifecycleService _lifecycleService;
  
  bool _isTracking = false;
  bool _isServiceRunning = false;
  String _status = 'غير محدد';

  @override
  void initState() {
    super.initState();
    _lifecycleService = Get.find<AppLifecycleService>();
    _checkStatus();
  }

  Future<void> _checkStatus() async {
    try {
      final tracking = await _locationService.isTrackingEnabled();

      setState(() {
        _isTracking = tracking;
        _isServiceRunning = tracking; // Native service runs when tracking is enabled
        _status = tracking ? 'يعمل' : 'متوقف';
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ: $e';
      });
    }
  }

  Future<void> _startTracking() async {
    try {
      final success = await _lifecycleService.startTracking();
      if (success) {
        _showMessage('تم تشغيل التتبع بنجاح');
        _checkStatus();
      } else {
        _showMessage('فشل في تشغيل التتبع');
      }
    } catch (e) {
      _showMessage('خطأ: $e');
    }
  }

  Future<void> _stopTracking() async {
    try {
      final success = await _lifecycleService.stopTracking();
      if (success) {
        _showMessage('تم إيقاف التتبع بنجاح');
        _checkStatus();
      } else {
        _showMessage('فشل في إيقاف التتبع');
      }
    } catch (e) {
      _showMessage('خطأ: $e');
    }
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار خدمة الموقع'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'حالة الخدمة',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text('تتبع الموقع: ${_isTracking ? "مفعل" : "معطل"}'),
                    Text('خدمة المراقبة: ${_isServiceRunning ? "تعمل" : "متوقفة"}'),
                    Text('الحالة العامة: $_status'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: _isTracking ? null : _startTracking,
              icon: const Icon(Icons.play_arrow),
              label: const Text('تشغيل التتبع'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton.icon(
              onPressed: !_isTracking ? null : _stopTracking,
              icon: const Icon(Icons.stop),
              label: const Text('إيقاف التتبع'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton.icon(
              onPressed: _checkStatus,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث الحالة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 30),
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تعليمات الاختبار:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text('1. اضغط "تشغيل التتبع"'),
                    const Text('2. تحقق من ظهور إشعار في شريط الإشعارات'),
                    const Text('3. أغلق التطبيق واتركه لدقائق'),
                    const Text('4. افتح التطبيق مرة أخرى'),
                    const Text('5. اضغط "تحديث الحالة" للتحقق'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
