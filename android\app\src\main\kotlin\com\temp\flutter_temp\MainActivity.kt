package com.temp.flutter_temp

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {

    companion object {
        private const val TAG = "MainActivity"
        private const val CHANNEL = "com.temp.flutter_temp/location_service"
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
        private const val PREFS_NAME = "location_tracking_prefs"
        private const val KEY_TRACKING_ENABLED = "location_tracking_enabled"
    }

    private var methodChannel: MethodChannel? = null
    private var locationService: LocationTrackingService? = null
    private var isServiceBound = false
    private lateinit var prefs: SharedPreferences

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as LocationTrackingService.LocationServiceBinder
            locationService = binder.getService()
            locationService?.methodChannel = methodChannel
            isServiceBound = true
            Log.d(TAG, "Service connected")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            locationService = null
            isServiceBound = false
            Log.d(TAG, "Service disconnected")
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "startLocationTracking" -> {
                    if (hasLocationPermissions()) {
                        startLocationTracking()
                        result.success(true)
                    } else {
                        requestLocationPermissions()
                        result.error("PERMISSION_DENIED", "Location permissions not granted", null)
                    }
                }
                "stopLocationTracking" -> {
                    stopLocationTracking()
                    result.success(true)
                }
                "isLocationTrackingEnabled" -> {
                    result.success(LocationTrackingService.isServiceRunning)
                }
                "hasLocationPermissions" -> {
                    result.success(hasLocationPermissions())
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Bind to service to establish communication
        bindLocationService()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isServiceBound) {
            unbindService(serviceConnection)
            isServiceBound = false
        }
    }

    private fun bindLocationService() {
        val intent = Intent(this, LocationTrackingService::class.java)
        bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    private fun startLocationTracking() {
        if (!hasLocationPermissions()) {
            Log.e(TAG, "Cannot start tracking: permissions not granted")
            return
        }

        try {
            val intent = Intent(this, LocationTrackingService::class.java).apply {
                action = "START_LOCATION_TRACKING"
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }

            // Save tracking state
            prefs.edit().putBoolean(KEY_TRACKING_ENABLED, true).apply()

            Log.d(TAG, "Location tracking started")
        } catch (e: Exception) {
            Log.e(TAG, "Error starting location tracking", e)
        }
    }

    private fun stopLocationTracking() {
        try {
            Log.d(TAG, "Stopping location tracking from MainActivity")

            // Save tracking state first
            prefs.edit().putBoolean(KEY_TRACKING_ENABLED, false).apply()

            // Send stop command to service
            val intent = Intent(this, LocationTrackingService::class.java).apply {
                action = "STOP_LOCATION_TRACKING"
            }

            // Stop the service
            stopService(intent)

            Log.d(TAG, "Location tracking stop command sent")

            // Notify Flutter about the change
            methodChannel?.invokeMethod("onTrackingStatusChanged", mapOf(
                "isTracking" to false
            ))
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping location tracking", e)
        }
    }

    private fun hasLocationPermissions(): Boolean {
        val fineLocation = ContextCompat.checkSelfPermission(
            this, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val coarseLocation = ContextCompat.checkSelfPermission(
            this, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        val backgroundLocation = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContextCompat.checkSelfPermission(
                this, Manifest.permission.ACCESS_BACKGROUND_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        return fineLocation && coarseLocation && backgroundLocation
    }

    private fun requestLocationPermissions() {
        val permissions = mutableListOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            permissions.add(Manifest.permission.ACCESS_BACKGROUND_LOCATION)
        }

        ActivityCompat.requestPermissions(
            this,
            permissions.toTypedArray(),
            LOCATION_PERMISSION_REQUEST_CODE
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            val allGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }

            methodChannel?.invokeMethod("onPermissionResult", mapOf(
                "granted" to allGranted
            ))

            if (allGranted) {
                Log.d(TAG, "All location permissions granted")
            } else {
                Log.w(TAG, "Some location permissions were denied")
            }
        }
    }
}
