// lib/features/map/controllers/map_controller.dart
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';

import '../../../core/services/location_database.dart';
import '../../home/<USER>/models/customer_visits.dart';

class MapViewController extends GetxController {
  RxList<LatLng> points = <LatLng>[].obs;
  RxBool isLoading = true.obs;

  Rx<DistributorVisits>? distributorVisits;

  @override
  void onInit() {
    super.onInit();
    
  }

loadData(){
loadLocations();
    loadDistributorVisits();
}
  var loadingDistributorVisits = false.obs;

  Future<void> loadDistributorVisits() async {
    print("home Started");
  }

  Future<void> loadLocations() async {
    try {
      final locations = await LocationDatabase.instance.getAllLocations();
      points.value = locations
          .map((loc) =>
              LatLng(loc['latitude'] as double, loc['longitude'] as double))
          .toList();
    } catch (e) {
      print('Error loading locations: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
