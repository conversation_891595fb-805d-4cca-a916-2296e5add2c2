import 'package:flutter/material.dart';
import 'package:flutter_temp/core/app_font.dart';
import 'package:flutter_temp/core/color_app.dart';
import 'package:get/get.dart';

class CustomizeText extends StatelessWidget {
  final String? title;
  final Color? color;
  final double? fontSize;
  final GestureTapCallback? press;
  final int? weight;
  final String? fontFamily;
  final int? overflow;
  final int? maxLines;
  final double? fontHeight;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;
  final TextDecoration? decoration;

  const CustomizeText(
      {super.key,
      this.maxLines,
      this.title,
      this.overflow,
      this.weight,
      this.color,
      this.fontSize,
      this.press,
      this.fontFamily,
      this.fontHeight,
      this.fontWeight = FontWeight.normal,
      this.textAlign,
      this.decoration});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: press,
      child: Text(
        title!.tr,
        style: TextStyle(
            decoration: decoration,
            color: color ?? ColorApp.colorText,
            fontSize: fontSize,
            height: fontHeight,
            fontFamily: fontFamily ?? Font.cairoRegular,
            fontWeight: fontWeight),
        textAlign: textAlign ?? TextAlign.start,
        overflow:
            (overflow == null) ? TextOverflow.visible : TextOverflow.ellipsis,
        maxLines: (maxLines == null) ? null : maxLines,
      ),
    );
  }
}
