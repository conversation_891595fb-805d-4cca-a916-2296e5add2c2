import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controllers/orders_controller.dart';
import 'order_details.dart';

class OrdersScreen extends GetView<OrdersController> {
  final String customer;
  const OrdersScreen(this.customer, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer Visits'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.fetchCustomerVisits(customer),
          ),
        ],
      ),
      body: GetBuilder<OrdersController>(initState: (_) {
        Future.delayed(Duration.zero, () async {
          controller.fetchCustomerVisits(customer);
        });
      }, builder: (controller) {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final visits = controller.filteredVisits;

        if (visits.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'No customer visits found',
                  style: TextStyle(fontSize: 18),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => controller.fetchCustomerVisits(customer),
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(8.0),
          itemCount: visits.length,
          itemBuilder: (context, index) {
            // Get.toNamed(Routes.order_details, arguments: order);
            final visit = visits[index];
            return GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => OrderDetailsScreen(visit: visit),
                  ),
                );
              },
              child: Card(
                elevation: 5,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Visit #${visit.name ?? "N/A"}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                            ),
                            Text(
                              'Customer: ${visit.customer ?? "N/A"}',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              'Date: ${visit.visitDate ?? "N/A"}',
                              style: const TextStyle(color: Colors.grey),
                            ),
                            if (visit.outstanding != null)
                              Text(
                                'Outstanding: \$${visit.outstanding?.toStringAsFixed(2)}',
                                style: const TextStyle(
                                    color: Colors.green, fontSize: 16),
                              ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _getStatusColor(visit.status ?? ""),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          visit.status ?? "Unknown",
                          style: const TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Open':
        return Colors.orange;
      case 'Completed':
        return Colors.green;
      case 'Cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
