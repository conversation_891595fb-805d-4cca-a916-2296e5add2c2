// ظظimport 'dart:async';

import 'package:get/get.dart';

import 'auth_provider.dart';
import 'controller/auth_controller.dart';

class AuthBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AuthProvider(),fenix: true);

    Get.lazyPut<AuthController>(
        () => AuthController(Get.find()),fenix: true);


   // Get.lazyPut<StreamProvider>(() => StreamProvider(),fenix: true);
   //    Get.lazyPut<StreamController>(()=>StreamController(Get.find(),Get.find()),fenix: true);

  }
}
