import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/app_font.dart';
import '../../../../core/color_app.dart';
import '../../../../core/widgets/custome_text.dart';
import '../../../../main.dart';
import '../../../../routes/app_routes.dart';

class EditProfileItem extends StatelessWidget {
  final String title;
  final String text;
  const EditProfileItem({super.key, required this.title, required this.text});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => _onTap(),
      child: Container(
        margin: const EdgeInsets.only(bottom: 9),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomizeText(
              title: title,
              textAlign: TextAlign.start,
              fontFamily: Font.cairoSemiBold,
              fontSize: 14,
              color: ColorApp.colorText,
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomizeText(
                  title: text,
                  textAlign: TextAlign.start,
                  fontFamily: Font.cairoSemiBold,
                  fontSize: 14,
                  color: const Color(0xFF86878B),
                ),
                const SizedBox(
                  width: 15,
                ),
                Icon(
                  (box.read("lang") == "ar")
                      ? Icons.keyboard_arrow_left_rounded
                      : Icons.keyboard_arrow_right_rounded,
                  color: const Color(0xFF86878B),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _onTap() {
    Get.toNamed(Routes.EditProfileView);
  }
}
