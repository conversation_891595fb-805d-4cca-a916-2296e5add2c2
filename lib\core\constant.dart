import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_temp/core/color_app.dart';
import 'package:flutter_temp/routes/app_routes.dart';
import 'package:get/get.dart';
// import 'app_widgets.dart';

var baseUrl ="https://distributor.rukn-software.com";// remoteConfig.getString("base_url"); //  "https://api.tikone.net/";
var baseApiUrl = "$baseUrl/api/method/";
const placeholder =
    "https://img.freepik.com/premium-vector/photo-icon-picture-icon-image-sign-symbol-vector-illustration_64749-4409.jpg?w=2000";
const placeholder1 =
    "https://cdn.shopify.com/s/files/1/0070/7032/files/trending-products_c8d0d15c-9afc-47e3-9ba2-f7bad0505b9b.png?format=webp&v=1614559651&width=1024";
const kPLACES_API_KEY = "AIzaSyA621obFOVSLFu86EQdS_yjmOZda0SmgAs";

showError(message) {
  // Get.snackbar("Sorry", message ?? "-",
  //     icon: Icon(Icons.error),
  //     // borderRadius: 0,
  //     // margin: EdgeInsets.all(8),
  //     // snackPosition: SnackPosition.BOTTOM,
  //     backgroundColor: Colors.orangeAccent);
  Get.showSnackbar(GetSnackBar(
    title: "Sorry".tr,
    message: message ?? "-",
    duration: const Duration(seconds: 3),
    backgroundColor: Colors.orangeAccent,
  ));
}

showSuccess(message) {
  Get.showSnackbar(GetSnackBar(
    title: "success".tr,
    message: message ?? "-",
    duration: const Duration(seconds: 3),
    backgroundColor: ColorApp.colorPrimary,
  ));

  // Get.snackbar("Done", message ?? "-",
  //     icon: Icon(
  //       Icons.check_circle,
  //       color: Colors.white,
  //     ),
  //     margin: EdgeInsets.all(8),
  //     colorText: Colors.white,
  //     snackPosition: SnackPosition.BOTTOM,
  //     backgroundColor: Colors.green);
}

// extension updateGetx on GetxController {
//   updateIFNeed() {
//     try {
//       update();
//     } catch (e) {}
//   }
// }

extension RandomInt on int {
  static int generate({int min = 0, required int max}) {
    final random = Random();
    return min + random.nextInt(max - min);
  }
}

goToProfile(id) {
  Get.toNamed(Routes.ProfileScreen,
      parameters: {"isProfileUser": "true", "userId": id});
}
