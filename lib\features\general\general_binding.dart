import 'package:get/get.dart';

import 'provider/general_provider.dart';
// import 'package:masaft_darb/features/general/presentation/controllers/general_controller.dart';
// import 'package:shared_preferences/shared_preferences.dart';

// import '../settings/data/settings_provider.dart';

class GeneralBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GeneralProvider(), fenix: true);

    // Get.lazyPut<GeneralController>(
    //     () => GeneralController());
    // Get.putAsync(() => SharedPreferences.getInstance(),permanent: true);
    // // Get.lazyPut(() => GeneralProvider();
    //     Get.lazyPut<SettingsProvider>(() => SettingsProvider());
 
    // Get.put(GeneralController(provider: Get.find(),settingsProvider: Get.find()),permanent: true);
  }
}
