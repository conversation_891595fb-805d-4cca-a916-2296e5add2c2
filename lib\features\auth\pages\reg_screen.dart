// import 'package:countries_utils/countries_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_segment/flutter_advanced_segment.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_temp/core/app_font.dart';
import 'package:flutter_temp/core/utility.dart';
import 'package:flutter_temp/core/widgets/button_app.dart';
import 'package:flutter_temp/core/widgets/custome_text.dart';
import 'package:get/get.dart';

import '../../../core/language.dart';
import '../../../core/widgets/textfield_app.dart';
import '../../../routes/app_routes.dart';
import '../controller/auth_controller.dart';

var fullPhone = "";

class RegUserScreen extends StatefulWidget {
  const RegUserScreen({super.key});

  @override
  _RegUserScreenState createState() => _RegUserScreenState();
}

class _RegUserScreenState extends State<RegUserScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _nameArController = TextEditingController();
  final TextEditingController _nameEnController = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();
  final GlobalKey<FormState> _fromKey = GlobalKey<FormState>();

  bool showClear = false;

  int currentStep = 0;
  int selectCountries = -1;
  final int _activeCurrentStep = 0;
  String phoneNumber = "";

  String MIN_DATETIME = '1950-05-12';
  String MAX_DATETIME = '2021-11-25';
  final String _format = 'yyyy-MMMM-dd';
  final TextEditingController _formatCtrl = TextEditingController();

  DateTime? _dateTime;
  // Here we have created list of steps
  // that are required to complete the form

  var isLoading = false;

  String dobText = "";
  @override
  void initState() {
    super.initState();
  }

  Widget _buildRegisterButton() {
    return Padding(
      padding: const EdgeInsetsDirectional.only(start: 20, end: 20),
      child: ButtonApp(
        title: "Sign Up".tr,
        press: () {
          if (_fromKey.currentState!.validate()) {
            {
              Get.toNamed(Routes.VerifyAccount);
              // regUser();
            }
          }
        },
      ),
    );
  }

  regUser() {
    if (_phoneNumberController.text.length < 6) {
      Get.snackbar("Sorry".tr, "Please check your phone number",
          snackPosition: SnackPosition.TOP, backgroundColor: Colors.orange);
      return;
    }
    if (_emailController.text.length < 6) {
      Get.snackbar("Sorry".tr, "Please check email",
          snackPosition: SnackPosition.TOP, backgroundColor: Colors.orange);
      return;
    }
    if (_nameArController.text.length < 6) {
      Get.snackbar("Sorry".tr, "Please check name",
          snackPosition: SnackPosition.TOP, backgroundColor: Colors.orange);
      return;
    }
    if (_nameEnController.text.length < 6) {
      Get.snackbar("Sorry".tr, "Please check name",
          snackPosition: SnackPosition.TOP, backgroundColor: Colors.orange);
      return;
    }
    if (dobText.length < 2) {
      Get.snackbar("Sorry".tr, "Please check DOB",
          snackPosition: SnackPosition.TOP, backgroundColor: Colors.orange);
      return;
    }

    // Get.find<AuthController>().register(
    //     name: _uerNameController.text,
    //     email: _emailController.text,
    //     password: _passwordController.text,
    //     phone_number: fullPhone,
    //     country: _countryController.text,
    //     date_of_birth: _dateController.text);
  }

  final _selectedSegment_02 = ValueNotifier('User');

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuthController>(
        // init: MyController(),
        initState: (_) {},
        builder: (controler) => Scaffold(
              backgroundColor: Colors.white,
              resizeToAvoidBottomInset: false,
              body: SafeArea(
                child: Stack(
                  fit: StackFit.expand,
                  // alignment: Alignment.topCenter,
                  children: [
                    // Positioned(
                    //   child: Image.asset(
                    //     "assets/images/background_login.png",
                    //     fit: BoxFit.fill,
                    //   ),
                    // ),
                    Positioned(
                        child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                bottom:
                                    MediaQuery.of(context).viewInsets.bottom),
                            child: SingleChildScrollView(
                              child: Container(
                                alignment: Alignment.center,
                                child: Form(
                                  key: _fromKey,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const SizedBox(height: 3),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          SafeArea(
                                            child: Container(
                                              margin: const EdgeInsets.only(
                                                  left: 20, right: 20, top: 20),
                                              child: const DropdownButtonWidget(),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 50.h),
                                      CustomizeText(
                                        title: "Let’s get started".tr,
                                        fontSize: 30,
                                        fontFamily: Font.cairoSemiBold,
                                      ),
                                      SizedBox(height: 15.h),
                                      AdvancedSegment(
                                        controller: _selectedSegment_02,
                                        segments: {
                                          'User': 'User'.tr,
                                          'Vendor': 'Vendor'.tr,
                                        },
                                        itemPadding: EdgeInsets.symmetric(
                                          // EdgeInsets
                                          horizontal: 50.w,
                                          vertical: 8.h,
                                        ),
                                        backgroundColor: const Color(0xFFF3F3F3),
                                        borderRadius: BorderRadius.circular(25),
                                        sliderDecoration: BoxDecoration(
                                          color: const Color(0xFF247E7B),
                                          borderRadius:
                                              BorderRadius.circular(25),
                                          border: Border.all(
                                            color: Colors.black,
                                            width: 2,
                                          ),
                                        ),
                                        activeStyle: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 30.h,
                                      ),
                                      ValueListenableBuilder<String>(
                                        valueListenable: _selectedSegment_02,
                                        builder: (_, key, __) {
                                          switch (key) {
                                            case 'User':
                                              return Column(
                                                children: [
                                                  Container(
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 20.w),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        CustomizeText(
                                                          title: "Name".tr,
                                                          fontSize: 16.sp,
                                                          fontFamily: Font
                                                              .cairoSemiBold,
                                                        ),
                                                        CustomizeText(
                                                          title: " *",
                                                          fontSize: 16.sp,
                                                          color: Colors.red,
                                                          fontFamily: Font
                                                              .cairoSemiBold,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    height: 8.h,
                                                  ),
                                                  Container(
                                                    margin: const EdgeInsets.only(
                                                        left: 20, right: 20),
                                                    child: TextFieldApp(
                                                        keyboardType:
                                                            TextInputType.name,
                                                        lable: "Name",
                                                        hint: "Enter Your Name"
                                                            .tr,
                                                        isBorder: true,
                                                        validator: (value) =>
                                                            Utility
                                                                .validatorGeneral(
                                                                    value),
                                                        controller:
                                                            _nameArController),
                                                  ),
                                                ],
                                              );
                                            case 'Vendor':
                                              return Column(
                                                children: [
                                                  Container(
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 20.w),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        CustomizeText(
                                                          title:
                                                              "Arabic Name".tr,
                                                          fontSize: 16.sp,
                                                          fontFamily: Font
                                                              .cairoSemiBold,
                                                        ),
                                                        CustomizeText(
                                                          title: " *",
                                                          fontSize: 16.sp,
                                                          color: Colors.red,
                                                          fontFamily: Font
                                                              .cairoSemiBold,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    height: 8.h,
                                                  ),
                                                  Container(
                                                    margin: const EdgeInsets.only(
                                                        left: 20, right: 20),
                                                    child: TextFieldApp(
                                                        keyboardType:
                                                            TextInputType.name,
                                                        lable: "Arabic Name",
                                                        hint:
                                                            "Enter Your Arabic Name"
                                                                .tr,
                                                        isBorder: true,
                                                        validator: (value) =>
                                                            Utility
                                                                .validatorGeneral(
                                                                    value),
                                                        controller:
                                                            _nameArController),
                                                  ),
                                                  const SizedBox(
                                                    height: 20,
                                                  ),
                                                  Container(
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 20.w),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        CustomizeText(
                                                          title:
                                                              "English Name".tr,
                                                          fontSize: 16.sp,
                                                          fontFamily: Font
                                                              .cairoSemiBold,
                                                        ),
                                                        CustomizeText(
                                                          title: " *",
                                                          fontSize: 16.sp,
                                                          color: Colors.red,
                                                          fontFamily: Font
                                                              .cairoSemiBold,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    height: 8.h,
                                                  ),
                                                  Container(
                                                    margin: const EdgeInsets.only(
                                                        left: 20, right: 20),
                                                    child: TextFieldApp(
                                                        keyboardType:
                                                            TextInputType.name,
                                                        lable: "English Name",
                                                        hint:
                                                            "Enter Your English Name"
                                                                .tr,
                                                        isBorder: true,
                                                        validator: (value) =>
                                                            Utility
                                                                .validatorGeneral(
                                                                    value),
                                                        controller:
                                                            _nameEnController),
                                                  ),
                                                ],
                                              );
                                            default:
                                              return const SizedBox();
                                          }
                                        },
                                      ),
                                      SizedBox(
                                        height: 20.h,
                                      ),
                                      Container(
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 20.w),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            CustomizeText(
                                              title: "email".tr,
                                              fontSize: 16.sp,
                                              fontFamily: Font.cairoSemiBold,
                                            ),
                                            CustomizeText(
                                              title: " *",
                                              fontSize: 16.sp,
                                              color: Colors.red,
                                              fontFamily: Font.cairoSemiBold,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 8.h,
                                      ),
                                      Container(
                                        margin: const EdgeInsets.only(
                                            left: 20, right: 20),
                                        child: TextFieldApp(
                                            keyboardType:
                                                TextInputType.emailAddress,
                                            lable: "Email",
                                            hint: "Enter Your Email".tr,
                                            isBorder: true,
                                            validator: (value) =>
                                                Utility.validatorGeneral(value),
                                            controller: _emailController),
                                      ),
                                      SizedBox(
                                        height: 8.h,
                                      ),
                                      Container(
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 20.w),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            CustomizeText(
                                              title: "Phone Number".tr,
                                              fontSize: 16.sp,
                                              fontFamily: Font.cairoSemiBold,
                                            ),
                                            CustomizeText(
                                              title: " *",
                                              fontSize: 16.sp,
                                              color: Colors.red,
                                              fontFamily: Font.cairoSemiBold,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 8.h,
                                      ),
                                      Container(
                                        margin: const EdgeInsets.only(
                                            left: 20, right: 20),
                                        child: TextFieldApp(
                                            keyboardType: TextInputType.phone,
                                            lable: "Phone",
                                            hint: "Enter Your Phone Number".tr,
                                            isBorder: true,
                                            validator: (value) =>
                                                Utility.validatorGeneral(value),
                                            controller: _phoneNumberController),
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 8.w),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Checkbox(
                                                value: controler.agreeTerms,
                                                activeColor: const Color(0xFF247E7B),
                                                onChanged: (bool? newValue) {
                                                  setState(() {
                                                    controler.agreeTerms =
                                                        newValue!;
                                                  });
                                                }),
                                            InkWell(
                                              onTap: () {
                                                // launch("https://saaad.net/itqan/site/page/3");
                                              },
                                              child: RichText(
                                                text: TextSpan(
                                                  text: "agreeterms".tr,
                                                  style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 16.sp,
                                                    fontFamily:
                                                        Font.cairoSemiBold,
                                                  ),
                                                  children: <TextSpan>[
                                                    TextSpan(
                                                      text: "T&C".tr,
                                                      style: TextStyle(
                                                        color:
                                                            const Color(0xFF247E7B),
                                                        fontSize: 16.sp,
                                                        fontFamily:
                                                            Font.cairoSemiBold,
                                                        decoration:
                                                            TextDecoration
                                                                .underline,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                              flex: 1,
                                              child: _buildRegisterButton()),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 30.h,
                                      ),
                                      InkWell(
                                        onTap: () {
                                          Get.toNamed(Routes.login);
                                        },
                                        child: Container(
                                          margin: EdgeInsets.symmetric(
                                              horizontal: 20.w),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              CustomizeText(
                                                title: "Already have an account"
                                                    .tr
                                                    .tr,
                                                fontSize: 16.sp,
                                                fontFamily: Font.cairoSemiBold,
                                              ),
                                              CustomizeText(
                                                title: "login".tr,
                                                fontSize: 16.sp,
                                                color: const Color(0xFF247E7B),
                                                fontFamily: Font.cairoSemiBold,
                                                fontWeight: FontWeight.w700,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 70.h,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ))
                  ],
                ),
              ),
            ));
  }
}
