import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/color_app.dart';
import '../../main.dart';
import '../../routes/app_routes.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  /// Creates a call page with given channel name.

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    Timer(const Duration(seconds: 1), () => checkLoginStatus());
  }

  checkLoginStatus() async {
    Get.toNamed(Routes.INDEX);

//    Get.off(RegUserScreen(), popGesture: true);
    if (box.read("token") != null &&
        box.read("token").toString().trim().isNotEmpty) {
      // Get.off(SelectSignInUp(), popGesture: true);
      // if (Get.find<GeneralController>().currentUser?. ?? false) {
      //   Get.offNamedUntil(Routes.INDEX, (route) => false);
      // } else {
      Get.offAllNamed(Routes.dashboard);
      //  Get.toNamed(Routes.VerifyAccount);
      // }
    } else {
      Get.toNamed(Routes.INDEX);
      // Get.toNamed(Routes.Login);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorApp.colorWhite,
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Positioned(
          //     child: SvgPicture.asset(
          //   "assets/images/splash.svg",
          //   fit: BoxFit.cover,
          //   width: double.infinity,
          //   height: double.infinity,
          // )),
          Positioned(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Padding(
              //   padding: EdgeInsets.all(50.h),
              //   child: Image.asset(
              //     "assets/images/logo.png",
              //     fit: BoxFit.contain,
              //   ),
              // ),
            ],
          ))
        ],
      ),
    );
  }
}
