import 'dart:ui';

import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:get/get_navigation/src/root/internacionalization.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';

import '../../main.dart';
import 'lang/type/ar.dart';
import 'lang/type/en.dart';

// ignore: import_of_legacy_library_into_null_safe
// import '../main.dart';
// import 'lang/en_us.dart';
// import 'lang/ar_eg.dart';

class LocalizationService extends Translations {
  // Default locale
/*  static final locale =
      Locale(box.read("lang"), box.read("langCode")); //Locale('en', 'US');*/
  static final locale =
     Locale(box.read("lang"), box.read("langCode") );// Locale("ar","Eg");
  // fallbackLocale saves the day when the locale gets in trouble

  // Supported languages
  // Needs to be same order with locales
  static final langs = [
    'English',
    'العربية',
  ];

  // Supported locales
  // Needs to be same order with langs
  static final locales = [
    const Locale('en', 'US'),
    const Locale('ar', 'EG'),
  ];

  // Keys and their translations
  // Translations are separated maps in `lang` file
  @override
  Map<String, Map<String, String>> get keys => {
        'en_US': enUS, // lang/en_us.dart
        'ar_EG': arEg, // lang/en_us.dart
      };

  // Gets locale from language, and updates the locale
  static void changeLocale(
    Locale locale,
  ) {
    // final locale = _getLocaleFromLanguage(lang);

    // LocalizationService.locale = locale;
    box.write("lang", locale.languageCode);
    box.write("langCode", locale.countryCode);
    Get.locale = locale;
    Get.updateLocale(locale);
  }

  // Finds language in `langs` list and returns it as Locale
  Locale? _getLocaleFromLanguage(String lang) {
    for (int i = 0; i < langs.length; i++) {
      if (lang == langs[i]) return locales[i];
    }
    return Get.locale;
  }
}