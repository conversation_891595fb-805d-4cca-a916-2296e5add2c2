class DistributorVisits {
  Message? message;

  DistributorVisits({this.message});

  DistributorVisits.fromJson(Map<String, dynamic> json) {
    message =
        json['message'] != null ? new Message.fromJson(json['message']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.message != null) {
      data['message'] = this.message!.toJson();
    }
    return data;
  }
}

class Message {
  Status? status;
  Data? data;

  Message({this.status, this.data});

  Message.fromJson(Map<String, dynamic> json) {
    status =
        json['status'] != null ? new Status.fromJson(json['status']) : null;
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.status != null) {
      data['status'] = this.status!.toJson();
    }
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Status {
  String? message;
  bool? success;
  int? code;

  Status({this.message, this.success, this.code});

  Status.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    success = json['success'];
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['success'] = this.success;
    data['code'] = this.code;
    return data;
  }
}

class Data {
  List<CustomerVisits>? customerVisits;

  Data({this.customerVisits});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['customer_visits'] != null) {
      customerVisits = <CustomerVisits>[];
      json['customer_visits'].forEach((v) {
        customerVisits!.add(new CustomerVisits.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.customerVisits != null) {
      data['customer_visits'] =
          this.customerVisits!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CustomerVisits {
  String? name;
  String? customer;
  String? status;
  String? visitDate;
  Coordinates? coordinates;
  double? outstanding;
  double? creditLimit;
  double? balance;

  CustomerVisits(
      {this.name,
      this.customer,
      this.status,
      this.visitDate,
      this.coordinates,
      this.outstanding,
      this.creditLimit,
      this.balance});

  CustomerVisits.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    customer = json['customer'];
    status = json['status'];
    visitDate = json['visit_date'];

    coordinates = json['coordinates'] != null
        ? Coordinates.fromJson(json['coordinates'])
        : null;
    outstanding = double.tryParse("${json['outstanding']}") ?? 0.0;
    creditLimit = double.tryParse("${json['credit_limit']}") ?? 0.0;
    balance = double.tryParse("${json['balance']}") ?? 0.0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['customer'] = this.customer;
    data['status'] = this.status;
    data['visit_date'] = this.visitDate;
    data['coordinates'] = this.coordinates;
    data['outstanding'] = this.outstanding;
    data['credit_limit'] = this.creditLimit;
    data['balance'] = this.balance;
    return data;
  }
}

//  "coordinates": {
//                         "lon": 72.897635,
//                         "lat": 19.090395
//                     },

class Coordinates {
  double? lon;
  double? lat;

  Coordinates({this.lon, this.lat});

  Coordinates.fromJson(Map<String, dynamic> json) {
    lon = json['lon'];
    lat = json['lat'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['lon'] = this.lon;
    data['lat'] = this.lat;
    return data;
  }
}
