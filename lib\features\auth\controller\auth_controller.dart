import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constant.dart';
import 'package:flutter_temp/core/widgets/loading_indicator.dart';
import 'package:get/get.dart';

// import '../../../constants/api_maneger.dart';
import '../../../main.dart';
import '../../../routes/app_routes.dart';
import '../../general/presentation/controllers/general_controller.dart';
import '../auth_provider.dart' as yone;

enum TypeLogin { facebook, google, apple }

enum TypePage { following, followers, non }

String? verificationId;

class AuthController extends GetxController {
  AuthController(this.provider);
  final yone.AuthProvider provider;

  bool agreeTerms = false;
  String? tempPhoneNum;
  int? resendToken;
  @override
  void onReady() {
    // authProvider
    //     .getCountries()
    //     .then((value) => countries = value.body?.data ?? []);
  }
  var generalController = Get.find<GeneralController>();
  var contentPageData = "";
  Future<void> login(
      {required String username, required String password}) async {
    print("Login Click");
    updateIFNeed();
    await provider
        .login(username: username, password: password)
        .then((userModel) async {
      print("Login ... ");
      var isOk = userModel.isOk ?? false;

      if (isOk && userModel.body?.message?.data?.token != null) {
        box.write("token", userModel.body?.message?.data?.token);
        generalController.setToken(userModel.body?.message?.data?.token ?? "");
        if (userModel.body?.message?.data?.employeeData != null) {
          generalController
              .setUser(userModel.body!.message!.data!.employeeData!);
        }
        box.save();
        // Get.offAllNamed(Routes.dashboard);
        Get.offNamedUntil(Routes.dashboard, (route) => false);
      } else {
        showError(userModel.body?.message?.status?.message ?? "-");
      }

      updateIFNeed();
    }).catchError((error, stackTrace) {
      print(error);
      print(stackTrace);

      print("Error");
      updateIFNeed();
    });

    print("Login fiish");
  }

  Widget loadingwg() {
    return Container(
      color: Colors.white.withOpacity(0.3),
      alignment: Alignment.center,
      width: Get.width,
      height: Get.height,
      child: const Stack(
        alignment: Alignment.center,
        children: [
          LoadingIndicator(),
        ],
      ),
    );
  }
}
