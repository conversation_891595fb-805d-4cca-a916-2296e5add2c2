// ignore_for_file: import_of_legacy_library_into_null_safe

import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as DIO;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';

import '../features/auth/model/main.dart';
import '../features/general/presentation/controllers/general_controller.dart';
import '../features/orders/data/models/DistributorStore.dart';
import '../main.dart';
import '../routes/app_routes.dart';
import 'constant.dart';
import 'widgets/loading_indicator.dart';

// Define an extension
extension BaseModel on Type {
  // fromJson(Map<String, dynamic> data) {}
  T fromJson<T>(Map<String, dynamic> json) {
    final map = {
      DistributorStore: (json) => DistributorStore.fromJson(json),
      Main: (json) => Main.fromJson(json),
    };

    if (!map.containsKey(T)) {
      throw Exception("Unknown type: $T");
    }

    return map[T]!(json) as T;
  }
}

class ApiProvider<T> extends GetConnect {
  static ApiProvider instance = ApiProvider();

  var showLoading = false;
  ApiProvider loading(loading) {
    showLoading = loading;
    // showLoading = false;
    return this;
  }

  ApiProvider() : super() {
    onInit();
  }

  @override
  void onInit() {
    httpClient.baseUrl = baseApiUrl;

    // httpClient.addRequestModifier<dynamic>((request) {
    //   debugPrint('╟ REQUEST ║ ${request.method.toUpperCase()}\n'
    //       '╟ url: ${request.url}\n'
    //       '╟ Headers: ${request.headers}\n');
    //   return request;
    // });

    // httpClient.addAuthenticator<dynamic>((request) async {
    //   request.headers.addAll(getHeader());
    //   return request;
    // });

    httpClient.addResponseModifier((request, response) {
      debugPrint(
        '\n╔══════════════════════════ Response ══════════════════════════\n'
        '╟ REQUEST ║ ${request.method.toUpperCase()}\n'
        '╟ url: ${request.url}\n'
        '╟ Headers: ${request.headers}\n'
        // 'Body: ${request?.bodyBytes?.map((event) => event.asMap().toString()) ?? ''}\n'
        '╟ Status Code: ${response.statusCode}\n'
        '╟ Data: ${response.bodyString?.toString() ?? ''}'
        '\n╚══════════════════════════ Response ══════════════════════════\n',
        wrapWidth: 1024,
      );

      return response;
    });
  }

  String? getUserID() {
    if ((Get.find<GeneralController>().token()) != null) {
      return Get.find<GeneralController>().currentUser?.employeeId;
    }
    return null;
  }

  getHeader(
      {bool enableContentType = true,
      String contentType = "application/json"}) {
    var headers = {"test": "test"};
    headers['lang'] = box.read("lang");
    headers['Accept-Language'] = box.read("lang");
    headers['os'] = Platform.isIOS ? "IOS" : "ANDROID";
    headers['Accept'] = "application/json";
    if (enableContentType) headers['Content-Type'] = contentType;
    headers['Accept-Charset'] = "utf-8";
    if ((Get.find<GeneralController>().token()) != null) {
      var token = '${(Get.find<GeneralController>().token())!}'; //'TEST'; //
      headers['Authorization'] = token;
      // print(token);
    }
    // print("headers");
    // print(headers);
    // print("Authorization Bearer${Get.find<GeneralController>().token()!}");
    return headers;
  }

  Future<Response<T>> sendGet<T>(url,
      {map, T Function(dynamic)? decoder, retryIfFailed = false}) async {
    // var history =
    //     DioCacheHistoryManager.getValidCacheResponse<T>("$url", "GET");
    // if (history != null) {
    // debugPrint("Cache|| LoadFromHistory");
    //   return history;
    // }
    if (showLoading) {
      try {
        showToastWidget(loadingwg(),
            dismissOtherToast: true, duration: const Duration(minutes: 10));
      } catch (_) {}
    }

    var response = await get("$url",
        query: map, headers: await getHeader(), decoder: decoder);
    response.printError();
    print(
        "Get Response    has error : ${response.hasError}  bodyString : ${response.bodyString}");
    if (showLoading) {
      try {
        dismissAllToast();
      } catch (_) {}
    }

    // Check for Expired Token in response
    if (response.bodyString != null) {
      try {
        var jsonResponse = jsonDecode(response.bodyString!);
        if (jsonResponse['message'] != null &&
            jsonResponse['message']['status'] != null &&
            jsonResponse['message']['status']['message'] == "Expired Token") {
          print("Expired Token detected in GET response");
          if (![Routes.SelectSignInUp, Routes.login, Routes.signUp]
              .contains(Get.currentRoute)) {
            Get.find<GeneralController>().logOut();
            return response;
          }
        }
      } catch (e) {
        print("Error parsing response: $e");
      }
    }

    if (response.hasError && retryIfFailed) {
      var retry = await showErrorNetwork(retry: sendGet, response: response);
      if (retry) {
        return await sendGet<T>(url,
            map: map, decoder: decoder, retryIfFailed: retryIfFailed);
      } else {
        // DioCacheHistoryManager.addtoCacheHistory(url, "Get", response);
        return response;
      }
    }
    // DioCacheHistoryManager.addtoCacheHistory(url, "Get", response);
    return response;
  }

  Future<Response<T>> sendPost<T>(url, dynamic body,
      {T Function(dynamic)? decoder,
      bool enableContentType = true,
      String contentType = "application/json",
      retryIfFailed = false,
      removeAuth = false}) async {
    print("$url");
    print(body);
    if (showLoading) {
      showToastWidget(loadingwg(),
          dismissOtherToast: true,
          duration: const Duration(minutes: 10)); // custom dialog
    }
    var header = await getHeader(
        contentType: contentType, enableContentType: enableContentType);
    if (removeAuth) header.remove("Authorization");
    if (removeAuth) header.remove("authorization");

    var response = await post("$url", body, headers: header, decoder: decoder);
    print(response.body.toString());
    if (showLoading) dismissAllToast();

    if (response.bodyString != null) {
      try {
        if ((response.statusCode ?? 0) > 400 &&
            (response.statusCode ?? 0) < 500) {
          print("Expired Token detected in POST response");
          if (![Routes.SelectSignInUp, Routes.login, Routes.signUp]
              .contains(Get.currentRoute)) {
            Get.find<GeneralController>().logOut();
            return response;
          }
        }
      } catch (e) {
        print("Error parsing response: $e");
      }
    }

    if (response.hasError && retryIfFailed) {
      var retry = await showErrorNetwork(response: response);
      if (retry) {
        return await sendPost<T>(url, body,
            decoder: decoder,
            contentType: contentType,
            retryIfFailed: retryIfFailed,
            enableContentType: enableContentType);
      } else {
        return response;
      }
    }

    return response;
  }

  Future<T> sendPostDio<T>(url, dynamic body,
      {T Function(dynamic)? decoder,
      bool enableContentType = true,
      String contentType = "application/json",
      retryIfFailed = false,
      removeAuth = false}) async {
    print("$url");
    print(body);
    if (showLoading) {
      showToastWidget(loadingwg(),
          dismissOtherToast: true,
          duration: const Duration(minutes: 10)); // custom dialog
    }
    var header = await getHeader(
        contentType: contentType, enableContentType: enableContentType);
    if (removeAuth) header.remove("Authorization");
    if (removeAuth) header.remove("authorization");

    var dio = DIO.Dio();
    print("$baseApiUrl$url");

    try {
      var response = await dio.request(
        "$baseApiUrl$url",
        options: DIO.Options(
          method: 'POST',
          headers: getHeader(),
        ),
        data: body,
      );

      log("""
url :${"$baseApiUrl$url"}
data: ${response.data}

""");

      // Check for Expired Token in response
      if (response.data != null && response.data is Map) {
        try {
          if (response.data['message'] != null &&
              response.data['message']['status'] != null &&
              response.data['message']['status']['message'] ==
                  "Expired Token") {
            print("Expired Token detected in DIO POST response");
            if (![Routes.SelectSignInUp, Routes.login, Routes.signUp]
                .contains(Get.currentRoute)) {
              Get.find<GeneralController>().logOut();
            }
          }
        } catch (e) {
          print("Error checking for expired token: $e");
        }
      }

      if (response.statusCode == 200) {
        print(json.encode(response.data));
      } else {
        print(response.statusMessage);
      }

      return T.fromJson<T>(response.data);
    } on DIO.DioException catch (e) {
      // Handle DioError which might contain token expiration info
      if (e.response?.statusCode == 400 &&
          (e.response?.statusCode ?? 0) < 500) {
        try {
          var responseData = e.response?.data;
          if (responseData != null &&
              responseData['message'] != null &&
              responseData['message']['status'] != null &&
              responseData['message']['status']['message'] == "Expired Token") {
            print("Expired Token detected in DIO error response");
            if (![Routes.SelectSignInUp, Routes.login, Routes.signUp]
                .contains(Get.currentRoute)) {
              Get.find<GeneralController>().logOut();
            }
          }
        } catch (parseError) {
          print("Error parsing DIO error response: $parseError");
        }
      }
      rethrow;
    }
  }

  Future<Response<T>> sendPut<T>(url, dynamic body,
      {T Function(dynamic)? decoder,
      bool enableContentType = true,
      String contentType = "application/json",
      retryIfFailed = false}) async {
    print("$url");
    print(body);
    if (showLoading) {
      showToastWidget(loadingwg(),
          dismissOtherToast: true,
          duration: const Duration(minutes: 10)); // custom dialog
    }

    var response = await put("$url", body,
        headers: await getHeader(
            contentType: contentType, enableContentType: enableContentType),
        decoder: decoder);
    if (showLoading) dismissAllToast();

    // Check for Expired Token in response
    if (response.bodyString != null) {
      try {
        if ((response.statusCode ?? 0) > 400 &&
            (response.statusCode ?? 0) < 500) {
          print("Expired Token detected in PUT response");
          if (![Routes.SelectSignInUp, Routes.login, Routes.signUp]
              .contains(Get.currentRoute)) {
            Get.find<GeneralController>().logOut();
            return response;
          }
        }
      } catch (e) {
        print("Error parsing response: $e");
      }
    }

    if (response.hasError && retryIfFailed) {
      var retry = await showErrorNetwork(response: response);
      if (retry) {
        return await sendPost<T>(url, body,
            decoder: decoder,
            contentType: contentType,
            retryIfFailed: retryIfFailed,
            enableContentType: enableContentType);
      } else {
        return response;
      }
    }

    return response;
  }

  Future<Response<T>> sendDelete<T>(url,
      {T Function(dynamic)? decoder,
      bool enableContentType = true,
      String contentType = "application/json",
      retryIfFailed = false}) async {
    print("$url");
    if (showLoading) {
      showToastWidget(loadingwg(),
          dismissOtherToast: true,
          duration: const Duration(minutes: 10)); // custom dialog
    }

    var response = await delete("$url",
        headers: await getHeader(
            contentType: contentType, enableContentType: enableContentType),
        decoder: decoder);
    if (showLoading) dismissAllToast();

    // Check for Expired Token in response
    if (response.bodyString != null) {
      try {
        if ((response.statusCode ?? 0) > 400 &&
            (response.statusCode ?? 0) < 500) {
          print("Expired Token detected in DELETE response");
          if (![Routes.SelectSignInUp, Routes.login, Routes.signUp]
              .contains(Get.currentRoute)) {
            Get.find<GeneralController>().logOut();
            return response;
          }
        }
      } catch (e) {
        print("Error parsing response: $e");
      }
    }

    if (response.hasError && retryIfFailed) {
      var retry = await showErrorNetwork(response: response);
      if (retry) {
        return await sendDelete<T>(url,
            decoder: decoder,
            contentType: contentType,
            retryIfFailed: retryIfFailed,
            enableContentType: enableContentType);
      } else {
        return response;
      }
    }

    return response;
  }

  Future<bool> showErrorNetwork(
      {Future<Response<T>>? Function(dynamic,
              {T Function(dynamic)? decoder,
              dynamic retryIfFailed,
              dynamic map})?
          retry,
      Response? response}) async {
    if (response != null && response.bodyString != null) {
      var mainModel = Main.fromJson(jsonDecode(response.bodyString!));
      if (mainModel.message?.status?.success == false ||
          (mainModel.message?.status?.message ?? "").isNotEmpty) {
        showError(mainModel.message?.status?.message ?? "-");
        print("SSSSSSSS");

        // Check for Expired Token message
        if ((response.statusCode ?? 0) > 400 &&
            (response.statusCode ?? 0) < 500) {
          if (![Routes.SelectSignInUp, Routes.login, Routes.signUp]
              .contains(Get.currentRoute)) {
            print("Token expired, logging out and redirecting to login");
            Get.find<GeneralController>().logOut();
            Get.offAllNamed(Routes.SelectSignInUp);
          }
        }
      }
      //Unauthenticated

      if (![Routes.SelectSignInUp, Routes.login, Routes.signUp]
          .contains(Get.currentRoute)) {
        if (response.statusCode == 400 && (response.statusCode ?? 0) < 500) {
          print("401 Unauthorized, logging out");
          Get.find<GeneralController>().logOut();
          Get.offAllNamed(Routes.SelectSignInUp);
        }
      }

      return false;
      // print();
    } else {
      return false;
      // var retry = await Get.to(ErrorView());
      // if (retry != null && retry == true) {
      //   return true;
      // } else {
      //   return false;
      // }
    }
  }
}

// OverlayEntry? overlayEntryOpacity;
// OverlayEntry? overlayEntryLoader;
//
// extension OverlayExt2 on GetInterface {
//   showLoadingOverlay({
//     Color opacityColor = Colors.black,
//     Widget? loadingWidget,
//     double opacity = .5,
//   }) async {
//     final navigatorState =
//         Navigator.of(Get.overlayContext!, rootNavigator: false);
//     final overlayState = navigatorState.overlay!;
//     if (overlayEntryOpacity != null) {
//       overlayEntryOpacity!.remove();
//       overlayEntryOpacity = null;
//     }
//     if (overlayEntryLoader != null) {
//       overlayEntryLoader!.remove();
//       overlayEntryLoader = null;
//     }
//
//     overlayEntryOpacity = OverlayEntry(builder: (context) {
//       return Opacity(
//           opacity: opacity,
//           child: Container(
//             color: opacityColor,
//           ));
//     });
//     overlayEntryLoader = OverlayEntry(builder: (context) {
//       return loadingWidget ??
//            loadingwg();
//     });
//     overlayState.insert(overlayEntryOpacity!);
//     overlayState.insert(overlayEntryLoader!);
//   }
//
//   Widget loadingwg() {
//     return Container(
//       color: Colors.black.withOpacity(0.4),
//       alignment: Alignment.center,
//       child: Stack(
//         alignment: Alignment.center,
//         children: [
//           SizedBox(
//               width: 80,
//               height: 80,
//               child: CircularProgressIndicator(
//                 backgroundColor: Colors.white,
//               )),
//           // SizedBox(
//           //     width: 50,
//           //     height: 50,
//           //     child: SvgPicture.asset("assets/svg/close.svg")),
//         ],
//       ),
//     );
//   }
//   removeOverlay() {
//     if (overlayEntryOpacity != null) {
//       overlayEntryOpacity!.remove();
//       overlayEntryOpacity = null;
//     }
//     if (overlayEntryLoader != null) {
//       overlayEntryLoader!.remove();
//       overlayEntryLoader = null;
//     }
//   }
// }
Widget loadingwg() {
  return Container(
    color: Colors.white.withOpacity(0.3),
    alignment: Alignment.center,
    width: Get.width,
    height: Get.height,
    child: const Stack(
      alignment: Alignment.center,
      children: [
        LoadingIndicator(),

        // SizedBox(
        //     width: 80,
        //     height: 80,
        //     child: CircularProgressIndicator(
        //       backgroundColor: Colors.blueAccent,
        //     )),
        // SizedBox(
        //     width: 50,
        //     height: 50,
        //     child:  LoadingAnimationWidget.threeArchedCircle(
        //       color: primaryColor,
        //       size: 50,

        //     )),
      ],
    ),
  );
}
