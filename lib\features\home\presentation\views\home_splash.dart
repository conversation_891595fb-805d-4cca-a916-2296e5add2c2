import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constants.dart';
import 'package:flutter_temp/core/widgets/app_button.dart';
import 'package:get/get.dart';

import '../../../../routes/app_routes.dart';

class Home extends StatelessWidget {
  const Home({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Constants.primaryColor,
      body: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 3,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  // Positioned(
                  //   top: 100.0,
                  //   left: 0.0,
                  //   right: 0.0,
                  //   child: Container(
                  //     height: 150.0,
                  //     width: double.infinity,
                  //     decoration: BoxDecoration(
                  //       image: DecorationImage(
                  //         image: AssetImage("assets/images/cloth_faded.png"),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  Container(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Image.network(
                        "https://wicm.ca/wp-content/uploads/2016/08/team-work-free-png-image.png",
                        height: 300.0,

                        // scale: 1.1,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20.0,
                  vertical: 24.0,
                ),
                decoration: const BoxDecoration(
                  color: Constants.scaffoldBackgroundColor,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(30.0),
                    topLeft: Radius.circular(30.0),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20.0),
                    Text(
                      "Welcome to RDistributor!",
                      style: Theme.of(context).textTheme.titleLarge!.copyWith(
                            fontWeight: FontWeight.w600,
                            color: const Color.fromRGBO(19, 22, 33, 1),
                          ),
                    ),
                    const SizedBox(
                      height: 10.0,
                    ),
                    const Text(
                      "This is the first version of our RDistributor app. Please sign in or create an account below.",
                      style: TextStyle(
                        color: Color.fromRGBO(74, 77, 84, 1),
                        fontSize: 14.0,
                      ),
                    ),
                    const SizedBox(
                      height: 40.0,
                    ),
                    // Let's create a generic button widget
                    AppButton(
                        text: "Log In",
                        type: ButtonType.PLAIN,
                        onPressed: () => Get.toNamed(Routes.login)),
                    const SizedBox(
                      height: 15.0,
                    ),
                    AppButton(
                      text: "Create an Account",
                      type: ButtonType.PRIMARY,
                      onPressed: () => {},
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
