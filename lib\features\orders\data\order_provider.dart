import 'dart:convert';

import 'package:flutter_temp/features/auth/model/main.dart';
import 'package:flutter_temp/features/orders/data/models/shopping_cart.dart';
import 'package:get/get.dart';

import '../../../core/api_provider.dart';
import 'models/DistributorStore.dart';

class OrderProvider extends Api<PERSON>rovider {
  Future<Response<Main>> make_payment(
          {
            required String customer,
            required String customer_visit,
          required double amount,
          mode_of_payment = "Cash"}) =>
      sendPost<Main>(
          'rukn_distributor.api.make_payment',
          {
            "customer": customer,
            "customer_visit": customer_visit,
            
            "amount": amount,
            "mode_of_payment": mode_of_payment
          },
          decoder: (obj) => Main.fromJson(obj));

  Future<DistributorStore> distributor_store({
    int limit_page_length = 400,
    int page = 1,
  }) =>
      sendPostDio<DistributorStore>(
          'rukn_distributor.api.distributor_store',
          {
            "limit_page_length": limit_page_length,
            "page": page,
          },
          contentType: "'application/json",
          enableContentType: true,
          decoder: (obj) => DistributorStore.fromJson(obj));

  Future<Main> create_invoicew({data}) => sendPostDio<Main>(
        'rukn_distributor.api.create_invoice',
        data,
      );

  Future<Response<Main>> create_invoice({data}) =>
      sendPost<Main>(
          'rukn_distributor.api.create_invoice',
         data,
          decoder: (obj) => Main.fromJson(obj));

  Future<CartStore> shopping_cart({required Map<String, dynamic> data}) async {
    var req = await sendPost(
      'rukn_distributor.api.shopping_cart',
      jsonEncode(data),
    );

    return CartStore.fromJson(req.body);
  }
}
