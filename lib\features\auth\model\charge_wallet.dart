class ChargeWallet {
  bool? status;
  Data? data;
  double? executionTime;
  String? message;

  ChargeWallet({this.status, this.data, this.executionTime, this.message});

  ChargeWallet.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    executionTime = json['execution_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['execution_time'] = executionTime;
    return data;
  }
}

class Data {
  String? paymentId;
  String? invoiceUrl;
  String? amount;
  String? currency;

  Data({this.paymentId, this.invoiceUrl, this.amount, this.currency});

  Data.fromJson(Map<String, dynamic> json) {
    paymentId = json['payment_id'];
    invoiceUrl = json['invoice_url'];
    amount = json['amount'];
    currency = json['currency'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['payment_id'] = paymentId;
    data['invoice_url'] = invoiceUrl;
    data['amount'] = amount;
    data['currency'] = currency;
    return data;
  }
}
