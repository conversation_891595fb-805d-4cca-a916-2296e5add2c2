// import 'dart:convert';

// import 'package:flutter_temp/features/auth/model/social_account_user.dart';
// import 'package:sign_in_with_apple/sign_in_with_apple.dart';

// class AppleIdLoginService {
//   AuthorizationCredentialAppleID? _appleID;
//   String? _encryptedEmail = "";
//   static AppleIdLoginService? _instance;

//   static AppleIdLoginService get instance {
//     _instance ??= AppleIdLoginService();

//     return _instance!;
//   }

//   Future<SocialAccountUser> login() async {
//     _appleID = await SignInWithApple.getAppleIDCredential(
//       scopes: [
//         AppleIDAuthorizationScopes.email,
//         AppleIDAuthorizationScopes.fullName,
//       ],
//     );
//     if (_appleID?.email == null) {
//       _encryptedEmail = json.decode(utf8.decode(base64.decode(
//           base64.normalize(_appleID!.identityToken!.split('.')[1]))))['email'];
//     }
//     final socialAccountUser = SocialAccountUser(
//         email: _appleID?.email ?? _encryptedEmail,
//         name: '${(_appleID?.givenName ?? '')} ${(_appleID?.familyName ?? '')}',
//         provider: 'apple',
//         providerId: _appleID?.userIdentifier,
//         token: _appleID?.identityToken);
//     return socialAccountUser;
//   }
// }
