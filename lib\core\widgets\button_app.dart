import 'package:flutter/material.dart';
import 'package:flutter_temp/core/app_font.dart';
import 'package:flutter_temp/core/widgets/custome_text.dart';

class ButtonApp extends StatelessWidget {
  final String title;
  final Color? Bcolor;
  final Color? fontColor;
  final GestureTapCallback? press;
  final Future<void>? pressFuture;
  const ButtonApp(
      {super.key,
      required this.title,
      this.press,
      this.pressFuture,
      this.Bcolor,
      this.fontColor});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: press,
      child: Container(
        height: 54,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Bcolor ?? const Color(0xFF247E7B),
          // gradient: LinearGradient(
          //   begin: Alignment(0.942, -1.0),
          //   end: Alignment(-0.962, 0.733),
          //   colors: [const Color(0xff7466ff), const Color(0xff00c9ff)],
          //   stops: [0.0, 1.0],
          // ),
          border: Border.all(color: fontColor ?? Colors.white, width: 2),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: CustomizeText(
          title: title,
          fontSize: 18,
          fontFamily: Font.cairoSemiBold,
          color: fontColor ?? Colors.white,
        ),
      ),
    );
  }
}
