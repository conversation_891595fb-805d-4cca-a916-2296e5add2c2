import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../main.dart';
import '../../../../routes/app_routes.dart';
import '../../../auth/model/login.dart';
import '../../../localization/localization_service.dart';

extension updateGetx on GetxController {
  updateIFNeed() {
    try {
      update();
    } catch (e) {}
  }
}

extension isLoginEXT on GetxController {
  bool get isLogin =>
      Get.isRegistered<GeneralController>() &&
      Get.find<GeneralController>().currentUser != null;
  bool get isNotLogin => Get.find<GeneralController>().currentUser == null;
}

class GeneralController extends GetxController with StateMixin {
  GeneralController();

  var codeLang = "".obs;
  changeUpdateLang(String lang) {
    codeLang.value = lang;
    update(['update_lang']);
  }

  initApp() {
    codeLang.value = box.read("lang") ?? "ar";
    update(['update_lang']);
  }

  @override
  void onReady() {
    change(null, status: RxStatus.success());
  }

  changeLang(Locale locale) {
    LocalizationService.changeLocale(locale);
    Get.updateLocale(locale);
    updateIFNeed();
  }

  setUser(EmployeeData user) async {
    // Get.find<SharedPreferences>()
    //     .setString("userModel", json.encode(user.toJson()));
    box.write("userModel", json.encode(user.toJson()));
    Get.log("setUser");

    // await box.save();
  }

  setToken(String token) async {
    box.write("token", token);
    Get.log("settoken");
    Get.log("settoken $token");
  }

  bool get isLogin =>
      box.read("token") != null &&
      box.read("token").toString().trim().isNotEmpty;
  clearUser() {
    Get.log("clearUser");

    // Clear all user-related data
    box.remove("userModel");
    box.remove("token");

    // Add any other user-related data that needs to be cleared
    // For example, if there are any cached API responses or user preferences
    // box.remove("cached_data");
    // box.remove("user_preferences");

    // Save changes to ensure data is properly cleared
    box.save();
  }

  String? token() {
    Get.log("currentUser");
    return box.hasData("token") ? box.read("token") : null;
  }

  EmployeeData? get currentUser {
    Get.log("currentUser");

    if (box.read("userModel") != null) {
      return EmployeeData.fromJson(json.decode(box.read("userModel")));
    } else {
      return null;
    }
  }

  logOut() async {
    Get.log("logOut() ");

    try {
      // await AuthProvider().removeCurrentTokenFcm();
    } catch (e) {}

    // Clear all user data
    clearUser();

    // Navigate to login screen with no ability to go back
    Future.delayed(const Duration(milliseconds: 100), () {
      Get.offAllNamed(Routes.INDEX);
    });
  }
}
