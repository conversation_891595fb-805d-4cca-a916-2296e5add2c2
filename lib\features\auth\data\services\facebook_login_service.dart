
// import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';

// import '../../model/social_account_user.dart';

// class FaceBookLoginService {
//   LoginResult? _loginResult;
//   static FaceBookLoginService? _instance;

//   static FaceBookLoginService get instance {
//     _instance ??= FaceBookLoginService();

//     return _instance!;
//   }

//   Future<SocialAccountUser> login() async {
//     _loginResult = await FacebookAuth.instance
//         .login(loginBehavior: LoginBehavior.dialogOnly);
//     if (_loginResult?.status == LoginStatus.success) {
//       final userData = await FacebookAuth.instance
//           .getUserData(fields: "email,name,id,picture.width(800).height(800)");
//       final socialAccountUser = SocialAccountUser(
//           email: userData['email'],
//           name: userData['name'],
//           provider: 'facebook',
//           providerId: _loginResult?.accessToken?.type.name,
//           token: _loginResult?.accessToken?.tokenString);
//       await FacebookAuth.instance.logOut();
//       print("FacebookAuth token  ${socialAccountUser.toString()}");
//       return socialAccountUser;
//     } else {
//       return SocialAccountUser();
//     }
//   }
// }
