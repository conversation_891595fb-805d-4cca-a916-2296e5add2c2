// import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
// import 'package:flutter_temp/core/widgets/dialog_loading.dart';
// import 'package:get/get.dart';

// import '../routes/app_routes.dart';
// import 'constant.dart';

// class DynamicLinksServise {
//   static init() {
//     FirebaseDynamicLinks.instance.onLink.listen((dynamicLinkData) {
//       handelPages(dynamicLinkData.link);
//     }).onError((error) {
//       // Handle errors
//     });
//   }

//   static offlineHandle() async {
//     // Check if you received the link via `getInitialLink` first
//     final PendingDynamicLinkData? initialLink =
//         await FirebaseDynamicLinks.instance.getInitialLink();

//     if (initialLink != null) {
//       final Uri deepLink = initialLink.link;
//       // Example of using the dynamic link to push the user to a different screen
//       handelPages(deepLink);
//     }
//   }

//   static handelPages(Uri link) async {
//     // Uri uri = Uri.parse(url);

//     String? id = link.queryParameters['id'];
//     String? type = link.queryParameters['type'];
//     // if (type == "stream") {
//     //    var loading = DialogLoading.of();
//     //    loading.show();
//     //    var provider = StreamProvider();
//     //    var details = await provider.streamDetails(
//     //        streamID: id);
//     //    loading.hide();
//     //    if (details.isOk && details.body?.data?.finishedAt == null) {
//     //      Get.to(StartLiveStream(
//     //        isBroadcaster: false,
//     //        streamID: id,
//     //      ));
//     //    } else {
//     //      showError("يبدو ان البث قد انتهي ");
//     //    }
//     //  } else if (type == "post" ) {
//     //    var loading = DialogLoading.of();
//     //    loading.show();
//     //    var provider = PostProvider();
//     //    var detils =
//     //        await provider.postDetails(id:id);
//     //    loading.hide();
//     //
//     //    if (detils.isOk && detils.body?.data != null) {
//     //      Get.to(VideoProfilePage(postList: [detils.body!.data!], index: 0));
//     //    }
//     //  } else if (type == "topic") {
//     //    var loading = DialogLoading.of();
//     //    loading.show();
//     //    var provider = TopicProvider();
//     //    var detils =
//     //        await provider.topicsDetails(id: id);
//     //    loading.hide();
//     //
//     //    if (detils.isOk && detils.body?.data != null) {
//     //      Get.to(PostDetailsPage(topic: detils.body!.data!));
//     //    }
//     //  }else if (type == "profile") {
//     //   Get.toNamed(Routes.ProfileScreen, parameters: {
//     //     "isProfileUser": "true",
//     //     "userId": id ?? ""
//     //   });
//     // }
//   }

//   static Future<String> createDynamicLink(
//     String id,
//     String type,
//   ) async {
//     var link = "${baseUrl}share?id=$id&type=$type";
//     final dynamicLinkParams = DynamicLinkParameters(
//       link: Uri.parse(link),
//       uriPrefix: "https://tikone.page.link",
//       androidParameters: const AndroidParameters(packageName: "net.yone.yone"),
//       iosParameters: const IOSParameters(bundleId: "com.sa.tikone"),
//     );

//     return (await FirebaseDynamicLinks.instance
//             .buildShortLink(dynamicLinkParams))
//         .shortUrl
//         .toString();
//   }
// }
