import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DatePickeryone extends StatelessWidget {
  DateTime? currentdate;
  Function(DateTime)? onDateTimeChanged;
  DatePickeryone({super.key, this.currentdate, this.onDateTimeChanged});
  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 200,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        Container(
          decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 0.5))),
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 10,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Selected date : ',
                style: TextStyle(color: Colors.grey, fontSize: 17),
              ),
              Text(
                DateFormat('MMM, dd yyyy').format(
                  currentdate ?? DateTime.now(),
                ),
                style: const TextStyle(color: Colors.blue, fontSize: 17),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 150,
          child: CupertinoDatePicker(
            initialDateTime: DateTime.now(),
            onDateTimeChanged: (DateTime newdate) {
              currentdate = newdate;
              onDateTimeChanged?.call(newdate);
              (context as Element).markNeedsBuild();
            },
            use24hFormat: false,
            maximumDate: DateTime.now(),
            minimumYear: 1970,
            maximumYear: 2023,
            minuteInterval: 1,
            mode: CupertinoDatePickerMode.date,
          ),
        ),
      ]),
    );
  }
}
