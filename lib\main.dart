import 'dart:io';


import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
// import 'package:camera/camera.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_temp/core/constants.dart';
import 'package:get/get.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:oktoast/oktoast.dart';

import 'core/notifications_service.dart';
import 'core/services/app_lifecycle_service.dart';

import 'core/services/location_service.dart';
import 'features/general/presentation/controllers/general_controller.dart';
import 'features/localization/localization_service.dart';
import 'firebase_options.dart';
import 'routes/app_pages.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Firebase.initializeApp();

  await init();

  runApp(const MyApp());
}

init() async {
  await GetStorage.init();
  Get.put(GeneralController(), permanent: true);
  Get.put(AppLifecycleService(), permanent: true);

  if (Platform.isIOS) {
    await Firebase.initializeApp();
  } else {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  }

  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  await NotificationService.init();
  // Pass all uncaught errors from the framework to Crashlytics.
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

  // Initialize native location service
  // No initialization needed for native service - it's handled by Android
}

final box = GetStorage();

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.

  @override
  Widget build(BuildContext context) {
    box.read("lang") ?? box.write("lang", "en");
    box.read("langCode") ?? box.write("langCode", "EN");

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) => GetMaterialApp(
              debugShowCheckedModeBanner: false,
              enableLog: true,
              localizationsDelegates: const [
                GlobalCupertinoLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
              ],
              getPages: AppPages.routes,
              initialRoute: AppPages.INITIAL,
              locale: LocalizationService.locale,
              translations: LocalizationService(),
              builder: (context, widget) {
                return OKToast(
                    child: GetBuilder<GeneralController>(
                        builder: (cont) => Directionality(
                            textDirection: box.read("lang") == "ar"
                                ? TextDirection.rtl
                                : TextDirection.ltr,
                            child: widget!)));
              },
              theme: ThemeData(
                scaffoldBackgroundColor: Constants.scaffoldBackgroundColor,

                visualDensity: VisualDensity.adaptivePlatformDensity,
                textTheme: GoogleFonts.poppinsTextTheme(),

                primarySwatch: Colors.green,
                // visualDensity: VisualDensity.adaptivePlatformDensity,
                appBarTheme: const AppBarTheme(
                  iconTheme: IconThemeData(color: Colors.white),
                  color: Constants.primaryColor,
                  foregroundColor: Colors.white,
                  systemOverlayStyle: SystemUiOverlayStyle(
                    //<-- SEE HERE
                    // Status bar color
                    statusBarColor: Colors.transparent,
                    statusBarIconBrightness: Brightness.dark,
                    statusBarBrightness: Brightness.light,
                  ),
                ),
              ),
            ));
  }
}
