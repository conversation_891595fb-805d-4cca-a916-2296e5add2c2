
class DataPage {
  bool? status;
  String? message;
  String? token;
  ContentPage? data;

  DataPage({this.status,  this.message, this.data,this.token});

  factory DataPage.fromJson(Map<String, dynamic> json) => DataPage(
        status: json['status'] as bool?,
        token: json['token'] as String?,
        message: json['message'] as String?,
        data: (json['data'] == null ||  json['data'] is! Map<String, dynamic>)
            ? null
            : ContentPage.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'status': status,
        'token': token,
        'message': message,
        'data': data?.toJson(),
      };
}
class ContentPage {
  ContentPage({
    required this.id,
    required this.slug,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
  });

  String id;
  String slug;
  String title;
  String content;
  DateTime createdAt;
  DateTime updatedAt;

  factory ContentPage.fromJson(Map<String, dynamic> json) => ContentPage(
    id: json["id"],
    slug: json["slug"],
    title: json["title"],
    content: json["content"],
    createdAt: DateTime.parse(json["created_at"]),
    updatedAt: DateTime.parse(json["updated_at"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "slug": slug,
    "title": title,
    "content": content,
    "created_at": createdAt.toIso8601String(),
    "updated_at": updatedAt.toIso8601String(),
  };
}