import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class DialogLoading {
  static late BuildContext _context;

  static DialogLoading of([BuildContext? context]) {
    if (context != null) {
      _context = context;
    } else {
      _context = Get.context!;
    }
    return DialogLoading();
  }

  void show() {
    showDialog(
      context: _context,
      barrierDismissible: false,
      builder: (context) {
        return _loading();
      },
    );
  }

  void hide() {
    Navigator.pop(_context);
  }

  Widget _loading() {
    return  const AlertDialog(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CupertinoActivityIndicator(),
            SizedBox(width: 20),
            Text(
              'الرجاء الإنتظار',
              style:
                  TextStyle(color:  Colors.black),
            ),
          ],
        ),
      );
    
  }
}
