import 'package:flutter_temp/features/home/<USER>/views/home_splash.dart';
import 'package:get/get.dart';

import '../features/auth/auth_binding.dart';
import '../features/auth/pages/edit_prfile_view.dart';
import '../features/auth/pages/login_screen.dart';
import '../features/auth/pages/reg_screen.dart';
import '../features/dashboard/dashboard.dart';
import '../features/general/general_binding.dart';
import '../features/home/<USER>';
import '../features/orders/order_binding.dart';
import '../features/orders/order_details.dart';
import '../features/orders/orders_binding.dart';
import '../features/orders/orders_view.dart';
import '../features/orders/single_order.dart';
import '../features/splash/splashScreen.dart';
import '../features/store/store_binding.dart';
import '../features/store/store_view.dart';
import 'app_routes.dart';

class AppPages {
  static const INITIAL = Routes.SPLASH;

  static final routes = [
    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashScreen(),
      // bindings: [SplashBinding(), GeneralBinding(), LoginBinding()],
    ),

    GetPage(
      name: Routes.signUp,
      // binding: AuthBinding(),
      bindings: [AuthBinding(), GeneralBinding()],
      page: () => const RegUserScreen(),
    ),
    // GetPage(
    //   name: Routes.EditProfileView,
    //   // binding: AuthBinding(),
    //   bindings: [
    //     AuthBinding(),
    //     GeneralBinding(),
    //   ],
    //   page: () => EditProfileFiledView(),
    // ),
    GetPage(
      name: Routes.EditProfileSettingsView,
      bindings: [
        AuthBinding(),
        GeneralBinding(),
      ],
      page: () => const EditProfileView(),
    ),

    GetPage(
      name: Routes.dashboard,
      // bindings: [AuthBinding(), GeneralBinding()],
      page: () => const MainTab(),
    ),

    GetPage(
      name: Routes.single_order,
      bindings: [OrderBinding(), GeneralBinding()],
      page: () => SingleOrder(),
    ),

    GetPage(
      name: Routes.order_details,
      bindings: [OrderBinding(), GeneralBinding()],
      page: () => OrderDetailsScreen(visit: Get.arguments),
    ),

    GetPage(
      name: Routes.login,
      bindings: [AuthBinding(), GeneralBinding()],
      page: () => LoginScreen(),
    ),

    GetPage(
      name: Routes.INDEX,
      preventDuplicates: false,
      bindings: [
        GeneralBinding(),
        HomeBinding(),
        StoreBinding(),
      ],
      page: () => const Home(),
    ),

    GetPage(
      name: Routes.store,
      bindings: [StoreBinding(), GeneralBinding()],
      page: () => const DistributorStoreScreen(),
    ),

    // GetPage(
    //   name: Routes.orders,
    //   bindings: [OrdersBinding(), GeneralBinding()],
    //   page: () => const OrdersScreen(),
    // ),
  ];
}

//  case "/":
//     return MaterialPageRoute(builder: (BuildContext context) {
//       return Home();
//     });
//   case "/login":
//     return MaterialPageRoute(builder: (BuildContext context) {
//       return Login();
//     });
//   case "/dashboard":
//     return MaterialPageRoute(builder: (BuildContext context) {
//       return Dashboard();
//     });
//   case "/single-order":
//     return MaterialPageRoute(builder: (BuildContext context) {
//       return SingleOrder();
//     });
//   default:
//     return MaterialPageRoute(builder: (BuildContext context) {
//       return Home();
//     });
