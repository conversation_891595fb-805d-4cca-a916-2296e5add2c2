class CartStore {
  Message? message;

  CartStore({this.message});

  CartStore.fromJson(Map<String, dynamic> json) {
    message =
        json['message'] != null ? new Message.fromJson(json['message']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.message != null) {
      data['message'] = this.message!.toJson();
    }
    return data;
  }
}

class Message {
  Status? status;
  Data? data;

  Message({this.status, this.data});

  Message.fromJson(Map<String, dynamic> json) {
    status =
        json['status'] != null ? new Status.fromJson(json['status']) : null;
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.status != null) {
      data['status'] = this.status!.toJson();
    }
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Status {
  String? message;
  bool? success;
  int? code;

  Status({this.message, this.success, this.code});

  Status.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    success = json['success'];
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['success'] = this.success;
    data['code'] = this.code;
    return data;
  }
}

class Data {
  Cart? cart;

  Data({this.cart});

  Data.fromJson(Map<String, dynamic> json) {
    cart = json['cart'] != null ? new Cart.fromJson(json['cart']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.cart != null) {
      data['cart'] = this.cart!.toJson();
    }
    return data;
  }
}

class Cart {
  String? name;
  String? owner;
  String? creation;
  String? modified;
  String? modifiedBy;
  int? docstatus;
  int? idx;
  String? title;
  String? namingSeries;
  String? customer;
  String? customerName;
  Null? taxId;
  String? orderType;
  String? transactionDate;
  String? deliveryDate;
  Null? poNo;
  Null? poDate;
  String? company;
  double? skipDeliveryNote;
  Null? amendedFrom;
  Null? costCenter;
  Null? project;
  String? currency;
  double? conversionRate;
  String? sellingPriceList;
  String? priceListCurrency;
  double? plcConversionRate;
  double? ignorePricingRule;
  Null? scanBarcode;
  String? setWarehouse;
  int? reserveStock;
  double? totalQty;
  double? totalNetWeight;
  double? baseTotal;
  double? baseNetTotal;
  double? total;
  double? netTotal;
  String? taxCategory;
  Null? taxesAndCharges;
  Null? shippingRule;
  Null? incoterm;
  Null? namedPlace;
  double? baseTotalTaxesAndCharges;
  double? totalTaxesAndCharges;
  double? baseGrandTotal;
  double? baseRoundingAdjustment;
  double? baseRoundedTotal;
  String? baseInWords;
  double? grandTotal;
  double? roundingAdjustment;
  double? roundedTotal;
  String? inWords;
  double? advancePaid;
  int? disableRoundedTotal;
  String? applyDiscountOn;
  double? baseDiscountAmount;
  Null? couponCode;
  double? additionalDiscountPercentage;
  double? discountAmount;
  Null? otherChargesCalculation;
  Null? customerAddress;
  Null? addressDisplay;
  Null? customerGroup;
  Null? territory;
  Null? contactPerson;
  Null? contactDisplay;
  Null? contactPhone;
  Null? contactMobile;
  Null? contactEmail;
  Null? shippingAddressName;
  Null? shippingAddress;
  Null? dispatchAddressName;
  Null? dispatchAddress;
  Null? companyAddress;
  Null? companyAddressDisplay;
  Null? paymentTermsTemplate;
  Null? tcName;
  Null? terms;
  String? status;
  String? deliveryStatus;
  double? perDelivered;
  double? perBilled;
  double? perPicked;
  String? billingStatus;
  Null? salesPartner;
  double? amountEligibleForCommission;
  double? commissionRate;
  double? totalCommission;
  double? loyaltyPoints;
  double? loyaltyAmount;
  Null? fromDate;
  Null? toDate;
  Null? autoRepeat;
  Null? letterHead;
  double? groupSameItems;
  Null? selectPrintHeading;
  String? language;
  int? isInternalCustomer;
  Null? representsCompany;
  Null? source;
  Null? interCompanyOrderReference;
  Null? campaign;
  Null? partyAccountCurrency;
  String? doctype;
  List<Items>? items;
  List<Null>? salesTeam;
  List<PaymentSchedule>? paymentSchedule;
  List<Null>? pricingRules;
  List<Null>? taxes;
  List<Null>? packedItems;

  Cart(
      {this.name,
      this.owner,
      this.creation,
      this.modified,
      this.modifiedBy,
      this.docstatus,
      this.idx,
      this.title,
      this.namingSeries,
      this.customer,
      this.customerName,
      this.taxId,
      this.orderType,
      this.transactionDate,
      this.deliveryDate,
      this.poNo,
      this.poDate,
      this.company,
      this.skipDeliveryNote,
      this.amendedFrom,
      this.costCenter,
      this.project,
      this.currency,
      this.conversionRate,
      this.sellingPriceList,
      this.priceListCurrency,
      this.plcConversionRate,
      this.ignorePricingRule,
      this.scanBarcode,
      this.setWarehouse,
      this.reserveStock,
      this.totalQty,
      this.totalNetWeight,
      this.baseTotal,
      this.baseNetTotal,
      this.total,
      this.netTotal,
      this.taxCategory,
      this.taxesAndCharges,
      this.shippingRule,
      this.incoterm,
      this.namedPlace,
      this.baseTotalTaxesAndCharges,
      this.totalTaxesAndCharges,
      this.baseGrandTotal,
      this.baseRoundingAdjustment,
      this.baseRoundedTotal,
      this.baseInWords,
      this.grandTotal,
      this.roundingAdjustment,
      this.roundedTotal,
      this.inWords,
      this.advancePaid,
      this.disableRoundedTotal,
      this.applyDiscountOn,
      this.baseDiscountAmount,
      this.couponCode,
      this.additionalDiscountPercentage,
      this.discountAmount,
      this.otherChargesCalculation,
      this.customerAddress,
      this.addressDisplay,
      this.customerGroup,
      this.territory,
      this.contactPerson,
      this.contactDisplay,
      this.contactPhone,
      this.contactMobile,
      this.contactEmail,
      this.shippingAddressName,
      this.shippingAddress,
      this.dispatchAddressName,
      this.dispatchAddress,
      this.companyAddress,
      this.companyAddressDisplay,
      this.paymentTermsTemplate,
      this.tcName,
      this.terms,
      this.status,
      this.deliveryStatus,
      this.perDelivered,
      this.perBilled,
      this.perPicked,
      this.billingStatus,
      this.salesPartner,
      this.amountEligibleForCommission,
      this.commissionRate,
      this.totalCommission,
      this.loyaltyPoints,
      this.loyaltyAmount,
      this.fromDate,
      this.toDate,
      this.autoRepeat,
      this.letterHead,
      this.groupSameItems,
      this.selectPrintHeading,
      this.language,
      this.isInternalCustomer,
      this.representsCompany,
      this.source,
      this.interCompanyOrderReference,
      this.campaign,
      this.partyAccountCurrency,
      this.doctype,
      this.items,
      this.salesTeam,
      this.paymentSchedule,
      this.pricingRules,
      this.taxes,
      this.packedItems});

  Cart.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    owner = json['owner'];
    creation = json['creation'];
    modified = json['modified'];
    modifiedBy = json['modified_by'];
    docstatus = json['docstatus'];
    idx = json['idx'];
    title = json['title'];
    namingSeries = json['naming_series'];
    customer = json['customer'];
    customerName = json['customer_name'];
    taxId = json['tax_id'];
    orderType = json['order_type'];
    transactionDate = json['transaction_date'];
    deliveryDate = json['delivery_date'];
    poNo = json['po_no'];
    poDate = json['po_date'];
    company = json['company'];
    skipDeliveryNote = _toDouble(json['skip_delivery_note']);
    amendedFrom = json['amended_from'];
    costCenter = json['cost_center'];
    project = json['project'];
    currency = json['currency'];
    conversionRate = _toDouble(json['conversion_rate']);
    sellingPriceList = json['selling_price_list'];
    priceListCurrency = json['price_list_currency'];
    plcConversionRate = _toDouble(json['plc_conversion_rate']);
    ignorePricingRule = _toDouble(json['ignore_pricing_rule']);
    scanBarcode = json['scan_barcode'];
    setWarehouse = json['set_warehouse'];
    reserveStock = json['reserve_stock'];
    totalQty = _toDouble(json['total_qty']);
    totalNetWeight = _toDouble(json['total_net_weight']);
    baseTotal = _toDouble(json['base_total']);
    baseNetTotal = _toDouble(json['base_net_total']);
    total = _toDouble(json['total']);
    netTotal = _toDouble(json['net_total']);
    taxCategory = json['tax_category'];
    taxesAndCharges = json['taxes_and_charges'];
    shippingRule = json['shipping_rule'];
    incoterm = json['incoterm'];
    namedPlace = json['named_place'];
    baseTotalTaxesAndCharges = _toDouble(json['base_total_taxes_and_charges']);
    totalTaxesAndCharges = _toDouble(json['total_taxes_and_charges']);
    baseGrandTotal = _toDouble(json['base_grand_total']);
    baseRoundingAdjustment = _toDouble(json['base_rounding_adjustment']);
    baseRoundedTotal = _toDouble(json['base_rounded_total']);
    baseInWords = json['base_in_words'];
    grandTotal = _toDouble(json['grand_total']);
    roundingAdjustment = _toDouble(json['rounding_adjustment']);
    roundedTotal = _toDouble(json['rounded_total']);
    inWords = json['in_words'];
    advancePaid = _toDouble(json['advance_paid']);
    disableRoundedTotal = json['disable_rounded_total'];
    applyDiscountOn = json['apply_discount_on'];
    baseDiscountAmount = _toDouble(json['base_discount_amount']);
    couponCode = json['coupon_code'];
    additionalDiscountPercentage =
        _toDouble(json['additional_discount_percentage']);
    discountAmount = _toDouble(json['discount_amount']);
    otherChargesCalculation = json['other_charges_calculation'];
    customerAddress = json['customer_address'];
    addressDisplay = json['address_display'];
    // customerGroup = json['customer_group'];
    territory = json['territory'];
    contactPerson = json['contact_person'];
    contactDisplay = json['contact_display'];
    contactPhone = json['contact_phone'];
    contactMobile = json['contact_mobile'];
    contactEmail = json['contact_email'];
    shippingAddressName = json['shipping_address_name'];
    shippingAddress = json['shipping_address'];
    dispatchAddressName = json['dispatch_address_name'];
    dispatchAddress = json['dispatch_address'];
    companyAddress = json['company_address'];
    companyAddressDisplay = json['company_address_display'];
    paymentTermsTemplate = json['payment_terms_template'];
    tcName = json['tc_name'];
    terms = json['terms'];
    status = json['status'];
    deliveryStatus = json['delivery_status'];
    perDelivered = _toDouble(json['per_delivered']);
    perBilled = _toDouble(json['per_billed']);
    perPicked = _toDouble(json['per_picked']);
    billingStatus = json['billing_status'];
    salesPartner = json['sales_partner'];
    amountEligibleForCommission =
        _toDouble(json['amount_eligible_for_commission']);
    commissionRate = _toDouble(json['commission_rate']);
    totalCommission = _toDouble(json['total_commission']);
    loyaltyPoints = _toDouble(json['loyalty_points']);
    loyaltyAmount = _toDouble(json['loyalty_amount']);
    fromDate = json['from_date'];
    toDate = json['to_date'];
    autoRepeat = json['auto_repeat'];
    letterHead = json['letter_head'];
    groupSameItems = _toDouble(json['group_same_items']);
    selectPrintHeading = json['select_print_heading'];
    language = json['language'];
    isInternalCustomer = json['is_internal_customer'];
    representsCompany = json['represents_company'];
    source = json['source'];
    interCompanyOrderReference = json['inter_company_order_reference'];
    campaign = json['campaign'];
    partyAccountCurrency = json['party_account_currency'];
    doctype = json['doctype'];
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }

    if (json['payment_schedule'] != null) {
      paymentSchedule = <PaymentSchedule>[];
      json['payment_schedule'].forEach((v) {
        paymentSchedule!.add(new PaymentSchedule.fromJson(v));
      });
    }
  }

  // Helper method to convert any numeric value to double
  static double? _toDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['owner'] = this.owner;
    data['creation'] = this.creation;
    data['modified'] = this.modified;
    data['modified_by'] = this.modifiedBy;
    data['docstatus'] = this.docstatus;
    data['idx'] = this.idx;
    data['title'] = this.title;
    data['naming_series'] = this.namingSeries;
    data['customer'] = this.customer;
    data['customer_name'] = this.customerName;
    data['tax_id'] = this.taxId;
    data['order_type'] = this.orderType;
    data['transaction_date'] = this.transactionDate;
    data['delivery_date'] = this.deliveryDate;
    data['po_no'] = this.poNo;
    data['po_date'] = this.poDate;
    data['company'] = this.company;
    data['skip_delivery_note'] = this.skipDeliveryNote;
    data['amended_from'] = this.amendedFrom;
    data['cost_center'] = this.costCenter;
    data['project'] = this.project;
    data['currency'] = this.currency;
    data['conversion_rate'] = this.conversionRate;
    data['selling_price_list'] = this.sellingPriceList;
    data['price_list_currency'] = this.priceListCurrency;
    data['plc_conversion_rate'] = this.plcConversionRate;
    data['ignore_pricing_rule'] = this.ignorePricingRule;
    data['scan_barcode'] = this.scanBarcode;
    data['set_warehouse'] = this.setWarehouse;
    data['reserve_stock'] = this.reserveStock;
    data['total_qty'] = this.totalQty;
    data['total_net_weight'] = this.totalNetWeight;
    data['base_total'] = this.baseTotal;
    data['base_net_total'] = this.baseNetTotal;
    data['total'] = this.total;
    data['net_total'] = this.netTotal;
    data['tax_category'] = this.taxCategory;
    data['taxes_and_charges'] = this.taxesAndCharges;
    data['shipping_rule'] = this.shippingRule;
    data['incoterm'] = this.incoterm;
    data['named_place'] = this.namedPlace;
    data['base_total_taxes_and_charges'] = this.baseTotalTaxesAndCharges;
    data['total_taxes_and_charges'] = this.totalTaxesAndCharges;
    data['base_grand_total'] = this.baseGrandTotal;
    data['base_rounding_adjustment'] = this.baseRoundingAdjustment;
    data['base_rounded_total'] = this.baseRoundedTotal;
    data['base_in_words'] = this.baseInWords;
    data['grand_total'] = this.grandTotal;
    data['rounding_adjustment'] = this.roundingAdjustment;
    data['rounded_total'] = this.roundedTotal;
    data['in_words'] = this.inWords;
    data['advance_paid'] = this.advancePaid;
    data['disable_rounded_total'] = this.disableRoundedTotal;
    data['apply_discount_on'] = this.applyDiscountOn;
    data['base_discount_amount'] = this.baseDiscountAmount;
    data['coupon_code'] = this.couponCode;
    data['additional_discount_percentage'] = this.additionalDiscountPercentage;
    data['discount_amount'] = this.discountAmount;
    data['other_charges_calculation'] = this.otherChargesCalculation;
    data['customer_address'] = this.customerAddress;
    data['address_display'] = this.addressDisplay;
    data['customer_group'] = this.customerGroup;
    data['territory'] = this.territory;
    data['contact_person'] = this.contactPerson;
    data['contact_display'] = this.contactDisplay;
    data['contact_phone'] = this.contactPhone;
    data['contact_mobile'] = this.contactMobile;
    data['contact_email'] = this.contactEmail;
    data['shipping_address_name'] = this.shippingAddressName;
    data['shipping_address'] = this.shippingAddress;
    data['dispatch_address_name'] = this.dispatchAddressName;
    data['dispatch_address'] = this.dispatchAddress;
    data['company_address'] = this.companyAddress;
    data['company_address_display'] = this.companyAddressDisplay;
    data['payment_terms_template'] = this.paymentTermsTemplate;
    data['tc_name'] = this.tcName;
    data['terms'] = this.terms;
    data['status'] = this.status;
    data['delivery_status'] = this.deliveryStatus;
    data['per_delivered'] = this.perDelivered;
    data['per_billed'] = this.perBilled;
    data['per_picked'] = this.perPicked;
    data['billing_status'] = this.billingStatus;
    data['sales_partner'] = this.salesPartner;
    data['amount_eligible_for_commission'] = this.amountEligibleForCommission;
    data['commission_rate'] = this.commissionRate;
    data['total_commission'] = this.totalCommission;
    data['loyalty_points'] = this.loyaltyPoints;
    data['loyalty_amount'] = this.loyaltyAmount;
    data['from_date'] = this.fromDate;
    data['to_date'] = this.toDate;
    data['auto_repeat'] = this.autoRepeat;
    data['letter_head'] = this.letterHead;
    data['group_same_items'] = this.groupSameItems;
    data['select_print_heading'] = this.selectPrintHeading;
    data['language'] = this.language;
    data['is_internal_customer'] = this.isInternalCustomer;
    data['represents_company'] = this.representsCompany;
    data['source'] = this.source;
    data['inter_company_order_reference'] = this.interCompanyOrderReference;
    data['campaign'] = this.campaign;
    data['party_account_currency'] = this.partyAccountCurrency;
    data['doctype'] = this.doctype;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }

    if (this.paymentSchedule != null) {
      data['payment_schedule'] =
          this.paymentSchedule!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class Items {
  String? name;
  String? owner;
  String? creation;
  String? modified;
  String? modifiedBy;
  int? docstatus;
  int? idx;
  String? itemCode;
  Null? customerItemCode;
  int? ensureDeliveryBasedOnProducedSerialNo;
  int? isStockItem;
  int? reserveStock;
  String? deliveryDate;
  String? itemName;
  String? description;
  String? itemGroup;
  Null? brand;
  String? image;
  int? qty;
  String? stockUom;
  String? uom;
  int? conversionFactor;
  int? stockQty;
  int? stockReservedQty;
  int? priceListRate;
  int? basePriceListRate;
  String? marginType;
  int? marginRateOrAmount;
  int? rateWithMargin;
  int? discountPercentage;
  int? discountAmount;
  int? baseRateWithMargin;
  int? rate;
  int? amount;
  Null? itemTaxTemplate;
  int? baseRate;
  int? baseAmount;
  Null? pricingRules;
  int? stockUomRate;
  int? isFreeItem;
  int? grantCommission;
  int? netRate;
  int? netAmount;
  int? baseNetRate;
  int? baseNetAmount;
  int? billedAmt;
  int? valuationRate;
  int? grossProfit;
  int? deliveredBySupplier;
  Null? supplier;
  int? weightPerUnit;
  int? totalWeight;
  Null? weightUom;
  String? warehouse;
  Null? targetWarehouse;
  Null? prevdocDocname;
  Null? quotationItem;
  int? againstBlanketOrder;
  Null? blanketOrder;
  int? blanketOrderRate;
  Null? bomNo;
  int? projectedQty;
  int? actualQty;
  int? orderedQty;
  int? plannedQty;
  int? productionPlanQty;
  int? workOrderQty;
  int? deliveredQty;
  int? producedQty;
  int? returnedQty;
  int? pickedQty;
  Null? additionalNotes;
  int? pageBreak;
  String? itemTaxRate;
  String? transactionDate;
  Null? materialRequest;
  Null? purchaseOrder;
  Null? materialRequestItem;
  Null? purchaseOrderItem;
  String? parent;
  String? parentfield;
  String? parenttype;
  String? doctype;
  int? iUnsaved;

  Items(
      {this.name,
      this.owner,
      this.creation,
      this.modified,
      this.modifiedBy,
      this.docstatus,
      this.idx,
      this.itemCode,
      this.customerItemCode,
      this.ensureDeliveryBasedOnProducedSerialNo,
      this.isStockItem,
      this.reserveStock,
      this.deliveryDate,
      this.itemName,
      this.description,
      this.itemGroup,
      this.brand,
      this.image,
      this.qty,
      this.stockUom,
      this.uom,
      this.conversionFactor,
      this.stockQty,
      this.stockReservedQty,
      this.priceListRate,
      this.basePriceListRate,
      this.marginType,
      this.marginRateOrAmount,
      this.rateWithMargin,
      this.discountPercentage,
      this.discountAmount,
      this.baseRateWithMargin,
      this.rate,
      this.amount,
      this.itemTaxTemplate,
      this.baseRate,
      this.baseAmount,
      this.pricingRules,
      this.stockUomRate,
      this.isFreeItem,
      this.grantCommission,
      this.netRate,
      this.netAmount,
      this.baseNetRate,
      this.baseNetAmount,
      this.billedAmt,
      this.valuationRate,
      this.grossProfit,
      this.deliveredBySupplier,
      this.supplier,
      this.weightPerUnit,
      this.totalWeight,
      this.weightUom,
      this.warehouse,
      this.targetWarehouse,
      this.prevdocDocname,
      this.quotationItem,
      this.againstBlanketOrder,
      this.blanketOrder,
      this.blanketOrderRate,
      this.bomNo,
      this.projectedQty,
      this.actualQty,
      this.orderedQty,
      this.plannedQty,
      this.productionPlanQty,
      this.workOrderQty,
      this.deliveredQty,
      this.producedQty,
      this.returnedQty,
      this.pickedQty,
      this.additionalNotes,
      this.pageBreak,
      this.itemTaxRate,
      this.transactionDate,
      this.materialRequest,
      this.purchaseOrder,
      this.materialRequestItem,
      this.purchaseOrderItem,
      this.parent,
      this.parentfield,
      this.parenttype,
      this.doctype,
      this.iUnsaved});

  Items.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    owner = json['owner'];
    creation = json['creation'];
    modified = json['modified'];
    modifiedBy = json['modified_by'];
    docstatus = json['docstatus'];
    idx = json['idx'];
    itemCode = json['item_code'];
    customerItemCode = json['customer_item_code'];
    ensureDeliveryBasedOnProducedSerialNo =
        json['ensure_delivery_based_on_produced_serial_no'];
    isStockItem = json['is_stock_item'];
    reserveStock = json['reserve_stock'];
    deliveryDate = json['delivery_date'];
    itemName = json['item_name'];
    description = json['description'];
    itemGroup = json['item_group'];
    brand = json['brand'];
    image = json['image'];
    qty = _toInt(json['qty']);
    stockUom = json['stock_uom'];
    uom = json['uom'];
    conversionFactor = _toInt(json['conversion_factor']);
    stockQty = _toInt(json['stock_qty']);
    stockReservedQty = _toInt(json['stock_reserved_qty']);
    priceListRate = _toInt(json['price_list_rate']);
    basePriceListRate = _toInt(json['base_price_list_rate']);
    marginType = json['margin_type'];
    marginRateOrAmount = _toInt(json['margin_rate_or_amount']);
    rateWithMargin = _toInt(json['rate_with_margin']);
    discountPercentage = _toInt(json['discount_percentage']);
    discountAmount = _toInt(json['discount_amount']);
    baseRateWithMargin = _toInt(json['base_rate_with_margin']);
    rate = _toInt(json['rate']);
    amount = _toInt(json['amount']);
    itemTaxTemplate = json['item_tax_template'];
    baseRate = _toInt(json['base_rate']);
    baseAmount = _toInt(json['base_amount']);
    pricingRules = json['pricing_rules'];
    stockUomRate = _toInt(json['stock_uom_rate']);
    isFreeItem = _toInt(json['is_free_item']);
    grantCommission = _toInt(json['grant_commission']);
    netRate = _toInt(json['net_rate']);
    netAmount = _toInt(json['net_amount']);
    baseNetRate = _toInt(json['base_net_rate']);
    baseNetAmount = _toInt(json['base_net_amount']);
    billedAmt = _toInt(json['billed_amt']);
    valuationRate = _toInt(json['valuation_rate']);
    grossProfit = _toInt(json['gross_profit']);
    deliveredBySupplier = _toInt(json['delivered_by_supplier']);
    supplier = json['supplier'];
    weightPerUnit = _toInt(json['weight_per_unit']);
    totalWeight = _toInt(json['total_weight']);
    weightUom = json['weight_uom'];
    warehouse = json['warehouse'];
    targetWarehouse = json['target_warehouse'];
    prevdocDocname = json['prevdoc_docname'];
    quotationItem = json['quotation_item'];
    againstBlanketOrder = _toInt(json['against_blanket_order']);
    blanketOrder = json['blanket_order'];
    blanketOrderRate = _toInt(json['blanket_order_rate']);
    bomNo = json['bom_no'];
    projectedQty = _toInt(json['projected_qty']);
    actualQty = _toInt(json['actual_qty']);
    orderedQty = _toInt(json['ordered_qty']);
    plannedQty = _toInt(json['planned_qty']);
    productionPlanQty = _toInt(json['production_plan_qty']);
    workOrderQty = _toInt(json['work_order_qty']);
    deliveredQty = _toInt(json['delivered_qty']);
    producedQty = _toInt(json['produced_qty']);
    returnedQty = _toInt(json['returned_qty']);
    pickedQty = _toInt(json['picked_qty']);
    additionalNotes = json['additional_notes'];
    pageBreak = _toInt(json['page_break']);
    itemTaxRate = json['item_tax_rate'];
    transactionDate = json['transaction_date'];
    materialRequest = json['material_request'];
    purchaseOrder = json['purchase_order'];
    materialRequestItem = json['material_request_item'];
    purchaseOrderItem = json['purchase_order_item'];
    parent = json['parent'];
    parentfield = json['parentfield'];
    parenttype = json['parenttype'];
    doctype = json['doctype'];
    iUnsaved = json['__unsaved'];
  }

  // Helper method to convert any numeric value to int
  static int? _toInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['owner'] = this.owner;
    data['creation'] = this.creation;
    data['modified'] = this.modified;
    data['modified_by'] = this.modifiedBy;
    data['docstatus'] = this.docstatus;
    data['idx'] = this.idx;
    data['item_code'] = this.itemCode;
    data['customer_item_code'] = this.customerItemCode;
    data['ensure_delivery_based_on_produced_serial_no'] =
        this.ensureDeliveryBasedOnProducedSerialNo;
    data['is_stock_item'] = this.isStockItem;
    data['reserve_stock'] = this.reserveStock;
    data['delivery_date'] = this.deliveryDate;
    data['item_name'] = this.itemName;
    data['description'] = this.description;
    data['item_group'] = this.itemGroup;
    data['brand'] = this.brand;
    data['image'] = this.image;
    data['qty'] = this.qty;
    data['stock_uom'] = this.stockUom;
    data['uom'] = this.uom;
    data['conversion_factor'] = this.conversionFactor;
    data['stock_qty'] = this.stockQty;
    data['stock_reserved_qty'] = this.stockReservedQty;
    data['price_list_rate'] = this.priceListRate;
    data['base_price_list_rate'] = this.basePriceListRate;
    data['margin_type'] = this.marginType;
    data['margin_rate_or_amount'] = this.marginRateOrAmount;
    data['rate_with_margin'] = this.rateWithMargin;
    data['discount_percentage'] = this.discountPercentage;
    data['discount_amount'] = this.discountAmount;
    data['base_rate_with_margin'] = this.baseRateWithMargin;
    data['rate'] = this.rate;
    data['amount'] = this.amount;
    data['item_tax_template'] = this.itemTaxTemplate;
    data['base_rate'] = this.baseRate;
    data['base_amount'] = this.baseAmount;
    data['pricing_rules'] = this.pricingRules;
    data['stock_uom_rate'] = this.stockUomRate;
    data['is_free_item'] = this.isFreeItem;
    data['grant_commission'] = this.grantCommission;
    data['net_rate'] = this.netRate;
    data['net_amount'] = this.netAmount;
    data['base_net_rate'] = this.baseNetRate;
    data['base_net_amount'] = this.baseNetAmount;
    data['billed_amt'] = this.billedAmt;
    data['valuation_rate'] = this.valuationRate;
    data['gross_profit'] = this.grossProfit;
    data['delivered_by_supplier'] = this.deliveredBySupplier;
    data['supplier'] = this.supplier;
    data['weight_per_unit'] = this.weightPerUnit;
    data['total_weight'] = this.totalWeight;
    data['weight_uom'] = this.weightUom;
    data['warehouse'] = this.warehouse;
    data['target_warehouse'] = this.targetWarehouse;
    data['prevdoc_docname'] = this.prevdocDocname;
    data['quotation_item'] = this.quotationItem;
    data['against_blanket_order'] = this.againstBlanketOrder;
    data['blanket_order'] = this.blanketOrder;
    data['blanket_order_rate'] = this.blanketOrderRate;
    data['bom_no'] = this.bomNo;
    data['projected_qty'] = this.projectedQty;
    data['actual_qty'] = this.actualQty;
    data['ordered_qty'] = this.orderedQty;
    data['planned_qty'] = this.plannedQty;
    data['production_plan_qty'] = this.productionPlanQty;
    data['work_order_qty'] = this.workOrderQty;
    data['delivered_qty'] = this.deliveredQty;
    data['produced_qty'] = this.producedQty;
    data['returned_qty'] = this.returnedQty;
    data['picked_qty'] = this.pickedQty;
    data['additional_notes'] = this.additionalNotes;
    data['page_break'] = this.pageBreak;
    data['item_tax_rate'] = this.itemTaxRate;
    data['transaction_date'] = this.transactionDate;
    data['material_request'] = this.materialRequest;
    data['purchase_order'] = this.purchaseOrder;
    data['material_request_item'] = this.materialRequestItem;
    data['purchase_order_item'] = this.purchaseOrderItem;
    data['parent'] = this.parent;
    data['parentfield'] = this.parentfield;
    data['parenttype'] = this.parenttype;
    data['doctype'] = this.doctype;
    data['__unsaved'] = this.iUnsaved;
    return data;
  }
}

class PaymentSchedule {
  String? name;
  String? owner;
  String? creation;
  String? modified;
  String? modifiedBy;
  int? docstatus;
  int? idx;
  Null? paymentTerm;
  Null? description;
  String? dueDate;
  Null? modeOfPayment;
  int? invoicePortion;
  Null? discountType;
  Null? discountDate;
  int? discount;
  int? paymentAmount;
  int? outstanding;
  int? paidAmount;
  int? discountedAmount;
  int? basePaymentAmount;
  String? parent;
  String? parentfield;
  String? parenttype;
  String? doctype;

  PaymentSchedule(
      {this.name,
      this.owner,
      this.creation,
      this.modified,
      this.modifiedBy,
      this.docstatus,
      this.idx,
      this.paymentTerm,
      this.description,
      this.dueDate,
      this.modeOfPayment,
      this.invoicePortion,
      this.discountType,
      this.discountDate,
      this.discount,
      this.paymentAmount,
      this.outstanding,
      this.paidAmount,
      this.discountedAmount,
      this.basePaymentAmount,
      this.parent,
      this.parentfield,
      this.parenttype,
      this.doctype});

  PaymentSchedule.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    owner = json['owner'];
    creation = json['creation'];
    modified = json['modified'];
    modifiedBy = json['modified_by'];
    docstatus = json['docstatus'];
    idx = json['idx'];
    paymentTerm = json['payment_term'];
    description = json['description'];
    dueDate = json['due_date'];
    modeOfPayment = json['mode_of_payment'];
    invoicePortion = _toInt(json['invoice_portion']);
    discountType = json['discount_type'];
    discountDate = json['discount_date'];
    discount = _toInt(json['discount']);
    paymentAmount = _toInt(json['payment_amount']);
    outstanding = _toInt(json['outstanding']);
    paidAmount = _toInt(json['paid_amount']);
    discountedAmount = _toInt(json['discounted_amount']);
    basePaymentAmount = _toInt(json['base_payment_amount']);
    parent = json['parent'];
    parentfield = json['parentfield'];
    parenttype = json['parenttype'];
    doctype = json['doctype'];
  }

  // Helper method to convert any numeric value to int
  static int? _toInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['owner'] = this.owner;
    data['creation'] = this.creation;
    data['modified'] = this.modified;
    data['modified_by'] = this.modifiedBy;
    data['docstatus'] = this.docstatus;
    data['idx'] = this.idx;
    data['payment_term'] = this.paymentTerm;
    data['description'] = this.description;
    data['due_date'] = this.dueDate;
    data['mode_of_payment'] = this.modeOfPayment;
    data['invoice_portion'] = this.invoicePortion;
    data['discount_type'] = this.discountType;
    data['discount_date'] = this.discountDate;
    data['discount'] = this.discount;
    data['payment_amount'] = this.paymentAmount;
    data['outstanding'] = this.outstanding;
    data['paid_amount'] = this.paidAmount;
    data['discounted_amount'] = this.discountedAmount;
    data['base_payment_amount'] = this.basePaymentAmount;
    data['parent'] = this.parent;
    data['parentfield'] = this.parentfield;
    data['parenttype'] = this.parenttype;
    data['doctype'] = this.doctype;
    return data;
  }
}
