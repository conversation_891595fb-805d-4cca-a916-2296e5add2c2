import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_temp/core/app_font.dart';
import 'package:flutter_temp/core/widgets/custome_text.dart';
import 'package:flutter_temp/main.dart';
import 'package:flutter_temp/routes/app_routes.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

appBar(
    {canBack = true,
    title = "",
    String? actionsIcon,
    Function? onActionClick,
    Function? onBackClick,
    bool centerTitle = false,
    bool isTransparent = false,
    Color? backGroudColor,
    List<Widget>? actions}) {
  return AppBar(
    backgroundColor:
        isTransparent ? Colors.transparent : backGroudColor ?? Colors.white,
    elevation: 0,
    centerTitle: centerTitle,
    leading: canBack
        ? Row(
            children: [
              const Gap(10),
              Container(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: InkWell(
                      onTap: () {
                        onBackClick == null
                            ? Navigator.canPop(Get.context!)
                                ? Get.back()
                                : Get.offAllNamed(Routes.INDEX)
                            : onBackClick.call();
                      },
                      child: RotatedBox(
                          quarterTurns: box.read("lang") == "ar" ? 2 : 0,
                          child: SvgPicture.asset("assets/images/back.svg"))),
                ),
              ),
            ],
          )
        : const SizedBox(),
    title: Text(
      title,
      textAlign: TextAlign.center,
      style: const TextStyle(
          color: Color.fromRGBO(17, 24, 39, 1),
          fontFamily: Font.cairoRegular,
          fontSize: 18,
          letterSpacing: 0.30000001192092896,
          fontWeight: FontWeight.bold,
          height: 1.5),
    ),
    actions: (actions != null && (actions.isNotEmpty))
        ? actions
        : actionsIcon == null
            ? null
            : [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: InkWell(
                      onTap: () => onActionClick?.call(),
                      child: SvgPicture.asset(actionsIcon)),
                ),
                const Gap(10),
              ],
  );
}

AppBar getAppbar(
  BuildContext context,
  String title, {
  hideBack = false,
  Function()? onBack,
  background,
  icon,
  Color? iconColor,
  bottomMargin,
  TextStyle? titleStyle,
}) {
  return AppBar(
    elevation: 0.0,
    backgroundColor: background ?? Colors.white,
    centerTitle: true,
    title: Container(
      margin: EdgeInsets.only(bottom: bottomMargin ?? 10),
      child: CustomizeText(
        title: title,
        fontSize: 22,
        fontFamily: Font.cairoSemiBold,
      ),
    ),
    leading: hideBack == true
        ? const SizedBox(
            width: 10,
          )
        : Container(
            margin: const EdgeInsets.only(bottom: 10),
            child: IconButton(
              icon: const Icon(
                Icons.arrow_back_outlined,
                color: Colors.black,
              ),
              onPressed: () {
                Get.back();
              },
            ),
          ),
  );
}

AppBar getAppbarWithLogo(title, {canBack = true, PreferredSizeWidget? bottom}) {
  return AppBar(
    elevation: 0,
    bottom: bottom,
    // backgroundColor: Colors.white,
    title: Text(
      title,
    ),
    /*Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          title,
          style: openSans(18, Color(0xff1F2124), FontWeight.w600),
        ),
       // SvgPicture.asset("assets/images/logo.svg")
      ],
    ),*/
    actions: const [],
    leading: canBack
        ? GestureDetector(
            onTap: () => Get.back(),
            child: const Icon(
              Icons.arrow_back_rounded,
              color: Colors.black,
            ),
          )
        : null,
    centerTitle: true,
  );
}
