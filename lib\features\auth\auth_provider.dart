import 'package:get/get.dart';

import '../../core/api_provider.dart';
import 'model/login.dart';

class AuthProvider extends ApiProvider {
  Future<Response<Login>> login({username, password, fcmToken}) =>
      loading(false).sendPost<Login>('rukn_distributor.api.login',
          {"username": username, "password": password},
          removeAuth: true,
          retryIfFailed: false,
          decoder: (obj) => Login.fromJson(obj));
}
