import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class LocationDatabase {
  static final LocationDatabase instance = LocationDatabase._init();
  static Database? _database;

  LocationDatabase._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('locations.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: 2,
      onCreate: _createDB,
      onUpgrade: _upgradeDB,
    );
  }

  Future<void> _upgradeDB(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Add customer_visit column to existing locations table
      await db.execute('ALTER TABLE locations ADD COLUMN customer_visit TEXT');

      // Create a new table for customer visits
      await db.execute('''
        CREATE TABLE customer_visits(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_name TEXT NOT NULL,
          visit_id TEXT NOT NULL,
          start_time TEXT NOT NULL,
          end_time TEXT,
          is_active INTEGER DEFAULT 1
        )
      ''');
    }
  }

  Future<void> _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE locations(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        timestamp TEXT NOT NULL,
        customer_visit TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE customer_visits(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_name TEXT NOT NULL,
        visit_id TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT,
        is_active INTEGER DEFAULT 1
      )
    ''');
  }

  Future<void> insert(Map<String, dynamic> location) async {
    final db = await instance.database;
    await db.insert('locations', location);
  }

  Future<List<Map<String, dynamic>>> getAllLocations() async {
    final db = await instance.database;
    return await db.query('locations', orderBy: 'timestamp DESC');
  }

  Future<List<Map<String, dynamic>>> getCustomerVisitLocations(
      String visitId) async {
    final db = await instance.database;
    return await db.query('locations',
        where: 'customer_visit = ?',
        whereArgs: [visitId],
        orderBy: 'timestamp ASC');
  }

  Future<void> deleteAllLocations() async {
    final db = await instance.database;
    await db.delete('locations');
  }

  // Customer visit methods
  Future<void> startCustomerVisit(String visitId, String customerName) async {
    final db = await instance.database;

    // End any existing active visits first
    await db.update(
      'customer_visits',
      {'is_active': 0, 'end_time': DateTime.now().toIso8601String()},
      where: 'is_active = ?',
      whereArgs: [1],
    );

    // Start new visit
    await db.insert('customer_visits', {
      'customer_name': customerName,
      'visit_id': visitId,
      'start_time': DateTime.now().toIso8601String(),
      'is_active': 1,
    });
  }

  Future<void> endCustomerVisit(String visitId) async {
    final db = await instance.database;
    await db.update(
      'customer_visits',
      {'is_active': 0, 'end_time': DateTime.now().toIso8601String()},
      where: 'visit_id = ? AND is_active = ?',
      whereArgs: [visitId, 1],
    );
  }

  Future<Map<String, dynamic>?> getActiveCustomerVisit() async {
    final db = await instance.database;
    final result = await db.query(
      'customer_visits',
      where: 'is_active = ?',
      whereArgs: [1],
      limit: 1,
    );

    return result.isNotEmpty ? result.first : null;
  }

  Future<List<Map<String, dynamic>>> getAllCustomerVisits() async {
    final db = await instance.database;
    return await db.query('customer_visits', orderBy: 'start_time DESC');
  }


}
