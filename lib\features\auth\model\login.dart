class Login {
  Message? message;

  Login({this.message});

  Login.fromJson(Map<String, dynamic> json) {
    message =
        json['message'] != null ? Message.fromJson(json['message']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (message != null) {
      data['message'] = message!.toJson();
    }
    return data;
  }
}

class Message {
  Status? status;
  Data? data;

  Message({this.status, this.data});

  Message.fromJson(Map<String, dynamic> json) {
    status =
        json['status'] != null ? Status.fromJson(json['status']) : null;
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (status != null) {
      data['status'] = status!.toJson();
    }
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Status {
  String? message;
  bool? success;
  int? code;

  Status({this.message, this.success, this.code});

  Status.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    success = json['success'];
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['success'] = success;
    data['code'] = code;
    return data;
  }
}

class Data {
  String? token;
  EmployeeData? employeeData;

  Data({this.token, this.employeeData});

  Data.fromJson(Map<String, dynamic> json) {
    token = json['token'];
    employeeData = json['employee_data'] != null
        ? EmployeeData.fromJson(json['employee_data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['token'] = token;
    if (employeeData != null) {
      data['employee_data'] = employeeData!.toJson();
    }
    return data;
  }
}

class EmployeeData {
  String? employeeId;
  String? dateOfJoining;
  Null companyEmail;
  Null contactNumber;

  EmployeeData(
      {this.employeeId,
      this.dateOfJoining,
      this.companyEmail,
      this.contactNumber});

  EmployeeData.fromJson(Map<String, dynamic> json) {
    employeeId = json['employee_id'];
    dateOfJoining = json['date_of_joining'];
    companyEmail = json['company_email'];
    contactNumber = json['contact_number'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['employee_id'] = employeeId;
    data['date_of_joining'] = dateOfJoining;
    data['company_email'] = companyEmail;
    data['contact_number'] = contactNumber;
    return data;
  }
}
