import 'dart:convert';

import 'package:dio/dio.dart' as dio;
import 'package:flutter/material.dart';
import 'package:flutter_temp/core/constant.dart';
import 'package:flutter_temp/features/general/presentation/controllers/general_controller.dart';
import 'package:flutter_temp/features/home/<USER>/home_provider.dart';
import 'package:flutter_temp/features/home/<USER>/models/customer_visits.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';

class OrderDetailsController extends GetxController {
  final HomeProvider homeProvider = HomeProvider();
  final Rx<CustomerVisits> visit;
  final RxBool isLoading = false.obs;

  OrderDetailsController({required CustomerVisits initialVisit})
      : visit = Rx<CustomerVisits>(initialVisit);

  // Refresh visit data
  Future<void> refreshVisitData() async {
    try {
      // This would typically call an API to get the latest data
      // For now, we'll just log that we would refresh the data
      print('Would refresh visit data for ID: ${visit.value.name}');
      // In a real implementation, you would call an API and update the visit data
    } catch (e) {
      print('Error refreshing visit data: $e');
    }
  }

  // Direct API call to update status
  Future<bool> updateStatusDirectApi(
      String customerVisitId, String status) async {
    try {
      final _dio = dio.Dio();

      // Print debug information
      print('Direct API call - Updating customer visit status:');
      print('Customer Visit ID: $customerVisitId');
      print('Status: $status');

      // Create FormData object exactly as shown in the example
      var formData = dio.FormData.fromMap(
          {"customer_visit": customerVisitId, "status": status});

      // Print the request URL
      final url = '${baseApiUrl}rukn_distributor.api.update_customer_visit';
      print('Request URL: $url');

      // Make the API call
      final response = await _dio.put(
        url,
        data: formData,
        options: dio.Options(
          headers: {
            'Authorization': (Get.find<GeneralController>().token())!,
          },
        ),
      );

      // Print the response for debugging
      print('Response status code: ${response.statusCode}');
      print('Response data: ${json.encode(response.data)}');

      return response.statusCode == 200;
    } catch (e) {
      print('Error in direct API call: $e');
      if (e is dio.DioException) {
        print('DioError type: ${e.type}');
        print('DioError message: ${e.message}');
        print('DioError response: ${e.response?.data}');
      }
      return false;
    }
  }

  // Update customer visit status
  Future<bool> updateVisitStatus(String status) async {
    if (visit.value.status == status) {
      return true; // No change needed
    }

    try {
      isLoading.value = true;

      // Print debug information
      print('Updating visit status from ${visit.value.status} to $status');
      print('Visit ID: ${visit.value.name}');

      // Try direct API call first
      bool success =
          await updateStatusDirectApi(visit.value.name ?? "", status);

      // If direct call fails, try using the provider
      if (!success) {
        print('Direct API call failed, trying through provider');
        final result = await homeProvider.updateCustomerVisitStatusDio(
            visit.value.name ?? "", // Using name as the ID
            status);

        success = result != null;
      }

      if (success) {
        print('API call successful, updating local state');
        // Update local state
        visit.update((val) {
          if (val != null) {
            val.status = status;
            print('Local state updated to: ${val.status}');
          }
        });

        // Refresh data to ensure we have the latest
        await refreshVisitData();

        // Show success message
        _showSuccessToast('Status updated to $status');
        return true;
      } else {
        // Show error message
        print('All API calls failed');
        _showErrorToast('Failed to update status');
        return false;
      }
    } catch (e) {
      // Handle error
      print('Error updating status: $e');
      _showErrorToast('Error occurred while updating status');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Get color for status
  Color getStatusColor(String status) {
    switch (status) {
      case 'Open':
        return Colors.orange;
      case 'Start':
        return Colors.blue;
      case 'Closed':
        return Colors.green;
      case 'Completed': // For backward compatibility
        return Colors.green;
      case 'Cancelled': // For backward compatibility
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // Helper method to show success toast
  void _showSuccessToast(String message) {
    showToastWidget(
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25.0),
          color: Colors.green,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check, color: Colors.white),
            const SizedBox(width: 12.0),
            Text(
              message,
              style: const TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
      position: ToastPosition.bottom,
      duration: const Duration(seconds: 3),
    );
  }

  // Helper method to show error toast
  void _showErrorToast(String message) {
    showToastWidget(
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25.0),
          color: Colors.red,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 12.0),
            Text(
              message,
              style: const TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
      position: ToastPosition.bottom,
      duration: const Duration(seconds: 3),
    );
  }
}
