import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

import '../constants.dart';

enum ButtonType { PRIMARY, PLAIN, SUCCESS, DANGER }

class AppButton extends StatefulWidget {
  final ButtonType type;
  // final VoidCallback onPressed;
  final Function() onPressed; // The future method to call

  final String text;

  const AppButton(
      {super.key,
      required this.type,
      required this.onPressed,
      required this.text});

  @override
  State<AppButton> createState() => _AppButtonState();
}

class _AppButtonState extends State<AppButton> {
  bool _isLoading = false; // Tracks the loading state
  Future<void> _handlePress() async {
    setState(() {
      _isLoading = true;
    });
    try {
      await widget.onPressed(); // Call the future method

      print("Finished");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _handlePress(), // _isLoading ? null : ,
      child: Container(
        width: double.infinity,
        height: ScreenUtil().setHeight(48.0),
        decoration: BoxDecoration(
          color: getButtonColor(widget.type),
          borderRadius: BorderRadius.circular(8.0),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(169, 176, 185, 0.42),
              spreadRadius: 0,
              blurRadius: 8.0,
              offset: Offset(0, 2),
            )
          ],
        ),
        child: Center(
          child: _isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : Text(
                  widget.text,
                  style: GoogleFonts.roboto(
                    color: getTextColor(widget.type),
                    fontSize: 16.0,
                    fontWeight: FontWeight.w500,
                  ),
                ),
        ),
      ),
    );
  }
}

Color getButtonColor(ButtonType type) {
  switch (type) {
    case ButtonType.PRIMARY:
      return Constants.primaryColor;
    case ButtonType.PLAIN:
      return Colors.white;
    case ButtonType.SUCCESS:
      return Colors.green;
    case ButtonType.DANGER:
      return Colors.red;
  }
}

Color getTextColor(ButtonType type) {
  switch (type) {
    case ButtonType.PLAIN:
      return Constants.primaryColor;
    case ButtonType.PRIMARY:
    case ButtonType.SUCCESS:
    case ButtonType.DANGER:
      return Colors.white;
  }
}
