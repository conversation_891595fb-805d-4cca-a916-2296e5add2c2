import 'package:flutter/material.dart';
import 'package:flutter_temp/core/app_font.dart';
import 'package:flutter_temp/core/color_app.dart';
import 'package:flutter_temp/core/widgets/custome_text.dart';

class CustomCheckBox extends StatefulWidget {
  const CustomCheckBox(this.label,
      {super.key,
      @required this.onSelect,
      this.selectedBackgroundColor,
      this.notSelectedBackgroundColor,
      this.selectedTextColor,
      this.notSelectedTextColor,
      this.height,
      this.width});

  final String? label;
  final ValueChanged<bool>? onSelect;
  final Color? selectedBackgroundColor;
  final Color? selectedTextColor;
  final Color? notSelectedBackgroundColor;
  final Color? notSelectedTextColor;
  final double? height;
  final double? width;

  @override
  _CustomCheckBoxState createState() => _CustomCheckBoxState();
}

class _CustomCheckBoxState extends State<CustomCheckBox> {
  bool _selected = true;

  void _onTap() {
    setState(() {
      _selected = !_selected;
    });
    widget.onSelect!(_selected);
  }

  @override
  Widget build(BuildContext context) {
    return TextButton(
//      textColor: textColor,

      onPressed: () {
        _onTap();
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Container(
            decoration: BoxDecoration(
              color: _selected ? ColorApp.colorPrimary : Colors.white,
              border: Border.all(
                  color: ColorApp.colorPrimary, // set border color
                  width: 2.0), // set border width
              borderRadius: const BorderRadius.all(Radius.circular(5.0)),

              // set rounded corner radius
            ),
            child: Padding(
              padding: const EdgeInsets.all(2.0),
              child: Icon(
                _selected ? Icons.check : Icons.check,
                size: 15,
                color: _selected ? Colors.white : ColorApp.colorPrimary,
              ),
            ),
          ),
          const SizedBox(
            width: 8,
          ),
          CustomizeText(
            title: widget.label!,
            fontSize: 12,
            textAlign: TextAlign.start,
            fontFamily: Font.cairoSemiBold,
          ),
          const SizedBox(
            width: 8,
          )
        ],
      ),
    );
  }
}
